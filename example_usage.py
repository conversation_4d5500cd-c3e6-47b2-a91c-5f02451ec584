#!/usr/bin/env python3
"""
Example usage of the VQA Generator
This script demonstrates how to use the VQA generator programmatically
"""

import os
import tempfile
import csv
from vqa_generator import VQAGenerator, VQAI<PERSON>

def create_sample_data():
    """Create a small sample CSV for demonstration"""
    sample_data = [
        {
            'Main Category': 'Architecture',
            'Question': '',
            'Option 1': '',
            'Option 2': '',
            'Option 3': '',
            'Option 4': '',
            'Correct Option': '',
            'Image Source': 'https://upload.wikimedia.org/wikipedia/commons/1/17/140526전주한옥마을_09.jpg',
            'Keyword/Concept': '한옥마을'
        },
        {
            'Main Category': 'Food',
            'Question': '',
            'Option 1': '',
            'Option 2': '',
            'Option 3': '',
            'Option 4': '',
            'Correct Option': '',
            'Image Source': '',
            'Keyword/Concept': '김치'
        },
        {
            'Main Category': 'Culture Attitude',
            'Question': '',
            'Option 1': '',
            'Option 2': '',
            'Option 3': '',
            'Option 4': '',
            'Correct Option': '',
            'Image Source': '',
            'Keyword/Concept': '정(情) 나누기'
        }
    ]
    
    # Create temporary CSV file
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8')
    fieldnames = ['Main Category', 'Question', 'Option 1', 'Option 2', 'Option 3', 'Option 4', 'Correct Option', 'Image Source', 'Keyword/Concept']
    
    writer = csv.DictWriter(temp_file, fieldnames=fieldnames)
    writer.writeheader()
    writer.writerows(sample_data)
    temp_file.close()
    
    return temp_file.name

def create_sample_rules():
    """Create sample generation rules"""
    rules_content = """
# Sample VQA Generation Rules

## Core Principles
- Questions must be challenging and require cultural expertise
- Focus on Korean cultural knowledge and context
- Require multi-step reasoning

## Question Requirements
- Expert-level difficulty
- Cultural authenticity
- 4 multiple choice options with plausible distractors
"""
    
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False, encoding='utf-8')
    temp_file.write(rules_content)
    temp_file.close()
    
    return temp_file.name

def example_basic_usage():
    """Example of basic VQA generation usage"""
    print("Example 1: Basic Usage")
    print("-" * 30)
    
    # You would normally use your actual files here
    rules_file = create_sample_rules()
    csv_file = create_sample_data()
    
    try:
        # Initialize the generator
        # Note: You need to provide a real OpenAI API key
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            print("Please set OPENAI_API_KEY environment variable to run this example")
            return
        
        generator = VQAGenerator(api_key, rules_file, csv_file)
        
        print(f"Loaded rules from: {rules_file}")
        print(f"Processing CSV: {csv_file}")
        
        # Process just the first few rows for demonstration
        print("\nProcessing CSV (this may take a few minutes due to API calls)...")
        
        # For demonstration, we'll show how you would call it
        # Uncomment the next line to actually run the generation
        # results = generator.process_csv()
        
        print("Note: Actual API calls commented out for demonstration")
        print("To run real generation, uncomment the process_csv() call")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        # Cleanup temporary files
        os.unlink(rules_file)
        os.unlink(csv_file)

def example_programmatic_usage():
    """Example of programmatic VQA item creation"""
    print("\nExample 2: Programmatic Usage")
    print("-" * 30)
    
    # Create VQA items programmatically
    sample_vqa = VQAItem(
        main_category="Architecture",
        question="Which traditional Korean architectural principle is most evident in the spatial organization of this hanok village layout?",
        option_1="Feng shui principles emphasizing harmony with natural topography and water flow patterns",
        option_2="Confucian hierarchy reflected in building heights and courtyard positioning relative to social status",
        option_3="Buddhist temple design influences with emphasis on symmetrical axis and meditation spaces",
        option_4="Japanese colonial period modifications prioritizing efficient land use over traditional aesthetics",
        correct_option="A",
        image_source="https://example.com/hanok-village.jpg",
        keyword_concept="한옥마을"
    )
    
    print("Sample VQA Item:")
    print(f"Category: {sample_vqa.main_category}")
    print(f"Question: {sample_vqa.question}")
    print(f"Options:")
    print(f"  A) {sample_vqa.option_1}")
    print(f"  B) {sample_vqa.option_2}")
    print(f"  C) {sample_vqa.option_3}")
    print(f"  D) {sample_vqa.option_4}")
    print(f"Correct Answer: {sample_vqa.correct_option}")
    print(f"Keyword: {sample_vqa.keyword_concept}")

def example_output_formats():
    """Example of different output formats"""
    print("\nExample 3: Output Formats")
    print("-" * 30)
    
    # Create sample results
    sample_results = [
        VQAItem(
            main_category="Food",
            question="Which fermentation principle is most crucial for achieving the optimal umami depth in traditional kimchi preparation?",
            option_1="Lactobacillus fermentation at precisely 4°C for exactly 21 days",
            option_2="Salt concentration of 2-3% with natural fermentation at room temperature for 3-7 days",
            option_3="Addition of jeotgal (fermented seafood) to introduce complex amino acid profiles during initial salting",
            option_4="Controlled anaerobic environment with daily stirring to prevent surface mold formation",
            correct_option="C",
            image_source="",
            keyword_concept="김치"
        )
    ]
    
    rules_file = create_sample_rules()
    csv_file = create_sample_data()
    
    try:
        # This would normally use a real API key
        generator = VQAGenerator("demo-key", rules_file, csv_file)
        
        # Save to CSV format
        csv_output = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False)
        csv_output.close()
        generator._save_to_csv(sample_results, csv_output.name)
        
        print(f"CSV output saved to: {csv_output.name}")
        
        # Save to JSON format  
        json_output = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        json_output.close()
        generator._save_to_json(sample_results, json_output.name)
        
        print(f"JSON output saved to: {json_output.name}")
        
        # Show CSV content
        print("\nCSV Output Preview:")
        with open(csv_output.name, 'r', encoding='utf-8') as f:
            print(f.read()[:200] + "...")
        
        # Show JSON content
        print("\nJSON Output Preview:")
        with open(json_output.name, 'r', encoding='utf-8') as f:
            print(f.read()[:200] + "...")
        
        # Cleanup
        os.unlink(csv_output.name)
        os.unlink(json_output.name)
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        os.unlink(rules_file)
        os.unlink(csv_file)

def main():
    """Run all examples"""
    print("VQA Generator Usage Examples")
    print("=" * 40)
    
    example_basic_usage()
    example_programmatic_usage()
    example_output_formats()
    
    print("\n" + "=" * 40)
    print("Examples completed!")
    print("\nTo run the actual VQA generator:")
    print("python vqa_generator.py --api-key YOUR_API_KEY --dry-run")

if __name__ == "__main__":
    main()
