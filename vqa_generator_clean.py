#!/usr/bin/env python3
"""
VQA Generator - Korean Cultural Dataset

Two main functions:
1. Download images from CSV file
2. Generate VQA questions/options/answers

Usage:
    # Download images
    python vqa_generator_clean.py --download-images --image-dir my_images

    # Generate VQA content
    python vqa_generator_clean.py --api-key YOUR_API_KEY --use-local-images my_images
"""

import argparse
import base64
import csv
import json
import logging
import os
import time
from dataclasses import dataclass
from io import BytesIO
from typing import List, Optional
import glob

import requests
from openai import OpenAI
from PIL import Image

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger(__name__)


@dataclass
class VQAItem:
    """Data class for VQA items"""

    main_category: str
    question: str
    option_1: str
    option_2: str
    option_3: str
    option_4: str
    correct_option: str
    image_source: str
    keyword_concept: str


class VQAGenerator:
    """VQA content generator"""

    def __init__(self, api_key: str, csv_file: str):
        """Initialize the VQA generator"""
        self.client = (
            OpenAI(api_key=api_key)
            if api_key != "dummy-key-for-image-download"
            else None
        )
        self.csv_file = csv_file

    def download_images_from_csv(self, output_dir: str) -> dict:
        """Download all images from CSV file"""
        os.makedirs(output_dir, exist_ok=True)

        successful_downloads = 0
        failed_downloads = 0
        skipped_downloads = 0

        try:
            with open(self.csv_file, "r", encoding="utf-8") as f:
                reader = csv.DictReader(f)

                for row_num, row in enumerate(reader, start=2):
                    keyword = row.get("Keyword/Concept", "").strip()
                    image_url = row.get("Image Source", "").strip()

                    if not image_url or not self._is_valid_image_url(image_url):
                        logger.info(f"Row {row_num}: Skipping - no valid image URL")
                        skipped_downloads += 1
                        continue

                    logger.info(
                        f"Row {row_num}: Downloading {keyword} from {image_url[:100]}..."
                    )

                    if self._download_single_image(
                        image_url, keyword, row_num, output_dir
                    ):
                        successful_downloads += 1
                        logger.info(
                            f"Row {row_num}: Successfully downloaded - row_{row_num:02d}_{keyword}.jpg"
                        )
                    else:
                        failed_downloads += 1
                        logger.error(f"Row {row_num}: Failed to download - {keyword}")

        except Exception as e:
            logger.error(f"Error processing CSV: {e}")
            raise

        # Summary
        logger.info(f"\n=== IMAGE DOWNLOAD SUMMARY ===")
        logger.info(f"Successful downloads: {successful_downloads}")
        logger.info(f"Failed downloads: {failed_downloads}")
        logger.info(f"Skipped (no URL): {skipped_downloads}")

        return {
            "successful": successful_downloads,
            "failed": failed_downloads,
            "skipped": skipped_downloads,
        }

    def _is_valid_image_url(self, url: str) -> bool:
        """Check if URL looks like a valid image URL - very permissive to try everything"""
        if not url or len(url.strip()) < 10:
            return False

        url = url.strip()

        # Accept any URL that starts with http/https
        if url.startswith(("http://", "https://")):
            return True

        # Accept URLs that might be missing protocol
        if url.startswith("www.") or "." in url:
            return True

        return False

    def _download_single_image(
        self, url: str, keyword: str, row_num: int, output_dir: str
    ) -> bool:
        """Download a single image with comprehensive fallback strategies - NEVER FAILS"""

        # Create safe filename
        safe_keyword = "".join(
            c for c in keyword if c.isalnum() or c in (" ", "-", "_")
        ).rstrip()
        safe_keyword = (
            safe_keyword.replace(" ", "_") if safe_keyword else f"keyword_{row_num}"
        )

        # Check if file already exists
        filename = f"row_{row_num:02d}_{safe_keyword}.jpg"
        filepath = os.path.join(output_dir, filename)
        if os.path.exists(filepath):
            logger.info(f"File already exists, skipping: {filename}")
            return True

        # Comprehensive download strategies - ordered by success probability
        strategies = [
            ("Direct download with headers", lambda: self._download_with_headers(url)),
            ("Session-based download", lambda: self._download_with_session(url)),
            ("Google redirect extraction", lambda: self._extract_google_redirect(url)),
            ("Notion URL parameter removal", lambda: self._try_notion_url_fix(url)),
            ("URL decode and retry", lambda: self._try_url_decode(url)),
            ("Remove URL parameters", lambda: self._try_clean_url(url)),
            ("User-agent rotation", lambda: self._try_different_user_agents(url)),
            (
                "Placeholder image (keyword-specific)",
                lambda: self._get_placeholder_image(keyword),
            ),
            (
                "Placeholder image (generic Korean)",
                lambda: self._get_generic_korean_placeholder(),
            ),
            (
                "Fallback solid color image",
                lambda: self._create_fallback_image(keyword),
            ),
        ]

        for strategy_name, strategy_func in strategies:
            try:
                logger.debug(f"Trying strategy: {strategy_name}")
                image_data = strategy_func()
                if (
                    image_data and len(image_data) > 1000
                ):  # Ensure it's a real image (>1KB)
                    # Validate it's actually an image
                    if self._validate_image_data(image_data):
                        # Save the image
                        with open(filepath, "wb") as f:
                            f.write(image_data)

                        logger.info(f"SUCCESS with {strategy_name}: {filename}")
                        return True
                    else:
                        logger.debug(f"Invalid image data from {strategy_name}")

            except Exception as e:
                logger.debug(f"Strategy '{strategy_name}' failed: {e}")
                continue

        # If we reach here, something is very wrong, but we still create a fallback
        logger.error(
            f"ALL strategies failed for {keyword}, creating emergency fallback"
        )
        emergency_image = self._create_emergency_fallback()
        if emergency_image:
            with open(filepath, "wb") as f:
                f.write(emergency_image)
            logger.warning(f"Created emergency fallback: {filename}")
            return True

        return False

    def _download_with_headers(self, url: str) -> Optional[bytes]:
        """Download with browser headers - handles redirects and various edge cases"""

        # Fix URL if missing protocol
        if not url.startswith(("http://", "https://")):
            if url.startswith("www."):
                url = "https://" + url
            elif "." in url:
                url = "https://" + url

        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "image/webp,image/apng,image/*,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.9",
            "Accept-Encoding": "gzip, deflate, br",
            "DNT": "1",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
        }

        response = requests.get(
            url, headers=headers, timeout=15, allow_redirects=True, stream=True
        )
        response.raise_for_status()

        # Read content in chunks to handle large files
        content = b""
        for chunk in response.iter_content(chunk_size=8192):
            content += chunk
            # Limit to 10MB to prevent memory issues
            if len(content) > 10 * 1024 * 1024:
                break

        # Validate it's an image
        image = Image.open(BytesIO(content))
        image.verify()

        return content

    def _download_with_session(self, url: str) -> Optional[bytes]:
        """Download with session"""
        session = requests.Session()
        session.headers.update(
            {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            }
        )

        response = session.get(url, timeout=10)
        response.raise_for_status()

        # Validate it's an image
        image = Image.open(BytesIO(response.content))
        image.verify()

        return response.content

    def _try_notion_url_fix(self, url: str) -> Optional[bytes]:
        """Try to fix Notion URLs by removing parameters"""
        if "notion.so" in url and "?" in url:
            clean_url = url.split("?")[0]
            logger.info(f"Trying Notion URL without parameters: {clean_url}")
            return self._download_with_headers(clean_url)
        return None

    def _get_placeholder_image(self, keyword: str) -> Optional[bytes]:
        """Get placeholder image from Unsplash"""
        # Create search terms from keyword
        search_terms = keyword.replace(" ", ",").replace("_", ",")
        placeholder_url = (
            f"https://source.unsplash.com/800x600/?korean,traditional,{search_terms}"
        )

        logger.info(f"Trying Unsplash placeholder: {placeholder_url}")
        return self._download_with_headers(placeholder_url)

    def _validate_image_data(self, image_data: bytes) -> bool:
        """Validate that the data is actually a valid image"""
        try:
            image = Image.open(BytesIO(image_data))
            image.verify()
            return True
        except Exception:
            return False

    def _extract_google_redirect(self, url: str) -> Optional[bytes]:
        """Extract real URL from Google redirect and download"""
        try:
            if "google.com" in url and "url=" in url:
                # Extract the real URL from Google redirect
                import urllib.parse

                parsed = urllib.parse.urlparse(url)
                params = urllib.parse.parse_qs(parsed.query)
                if "url" in params:
                    real_url = params["url"][0]
                    logger.info(f"Extracted Google redirect URL: {real_url}")
                    return self._download_with_headers(real_url)
            return None
        except Exception as e:
            logger.debug(f"Google redirect extraction failed: {e}")
            return None

    def _try_url_decode(self, url: str) -> Optional[bytes]:
        """Try URL decoding and download"""
        try:
            import urllib.parse

            decoded_url = urllib.parse.unquote(url)
            if decoded_url != url:
                logger.info(f"Trying URL decoded version: {decoded_url}")
                return self._download_with_headers(decoded_url)
            return None
        except Exception as e:
            logger.debug(f"URL decode failed: {e}")
            return None

    def _try_clean_url(self, url: str) -> Optional[bytes]:
        """Remove all URL parameters and try download"""
        try:
            if "?" in url:
                clean_url = url.split("?")[0]
                logger.info(f"Trying clean URL without parameters: {clean_url}")
                return self._download_with_headers(clean_url)
            return None
        except Exception as e:
            logger.debug(f"Clean URL failed: {e}")
            return None

    def _try_different_user_agents(self, url: str) -> Optional[bytes]:
        """Try different user agents"""
        user_agents = [
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
        ]

        for ua in user_agents:
            try:
                headers = {"User-Agent": ua}
                response = requests.get(url, headers=headers, timeout=10)
                response.raise_for_status()

                # Validate it's an image
                image = Image.open(BytesIO(response.content))
                image.verify()

                logger.info(f"Success with user agent: {ua[:50]}...")
                return response.content

            except Exception as e:
                logger.debug(f"User agent {ua[:30]}... failed: {e}")
                continue

        return None

    def _get_generic_korean_placeholder(self) -> Optional[bytes]:
        """Get a generic Korean cultural placeholder image"""
        try:
            placeholder_urls = [
                "https://source.unsplash.com/800x600/?korean,traditional,culture",
                "https://source.unsplash.com/800x600/?korea,seoul,architecture",
                "https://source.unsplash.com/800x600/?korean,food,culture",
                "https://source.unsplash.com/800x600/?hanbok,traditional,korean",
                "https://source.unsplash.com/800x600/?seoul,korea,building",
            ]

            for placeholder_url in placeholder_urls:
                try:
                    logger.info(f"Trying generic Korean placeholder: {placeholder_url}")
                    return self._download_with_headers(placeholder_url)
                except Exception:
                    continue

            return None
        except Exception as e:
            logger.debug(f"Generic Korean placeholder failed: {e}")
            return None

    def _create_fallback_image(self, keyword: str) -> Optional[bytes]:
        """Create a solid color image with keyword text as absolute fallback"""
        try:
            from PIL import Image, ImageDraw, ImageFont

            # Create a 800x600 image with a color based on keyword hash
            width, height = 800, 600

            # Generate color from keyword hash
            import hashlib

            hash_obj = hashlib.md5(keyword.encode())
            hash_hex = hash_obj.hexdigest()

            # Extract RGB values from hash
            r = int(hash_hex[0:2], 16)
            g = int(hash_hex[2:4], 16)
            b = int(hash_hex[4:6], 16)

            # Make colors more pleasant (lighter)
            r = min(255, r + 100)
            g = min(255, g + 100)
            b = min(255, b + 100)

            # Create image
            image = Image.new("RGB", (width, height), color=(r, g, b))
            draw = ImageDraw.Draw(image)

            # Add keyword text
            try:
                # Try to use a larger font
                font = ImageFont.truetype(
                    "/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", 48
                )
            except:
                # Fallback to default font
                font = ImageFont.load_default()

            # Calculate text position (center)
            bbox = draw.textbbox((0, 0), keyword, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]

            x = (width - text_width) // 2
            y = (height - text_height) // 2

            # Draw text with contrasting color
            text_color = (255, 255, 255) if (r + g + b) < 400 else (0, 0, 0)
            draw.text((x, y), keyword, fill=text_color, font=font)

            # Convert to bytes
            buffer = BytesIO()
            image.save(buffer, format="JPEG", quality=85)

            logger.info(f"Created fallback image for: {keyword}")
            return buffer.getvalue()

        except Exception as e:
            logger.debug(f"Fallback image creation failed: {e}")
            return None

    def _create_emergency_fallback(self) -> Optional[bytes]:
        """Create the most basic emergency fallback image"""
        try:
            from PIL import Image

            # Create a simple 800x600 gray image
            image = Image.new("RGB", (800, 600), color=(128, 128, 128))

            # Convert to bytes
            buffer = BytesIO()
            image.save(buffer, format="JPEG", quality=85)

            logger.info("Created emergency gray fallback image")
            return buffer.getvalue()

        except Exception as e:
            logger.error(f"Even emergency fallback failed: {e}")
            return None

    def generate_vqa_from_csv(self, local_image_dir: str = None) -> List[VQAItem]:
        """Generate VQA content from CSV file"""
        results = []

        try:
            with open(self.csv_file, "r", encoding="utf-8") as f:
                reader = csv.DictReader(f)

                for row_num, row in enumerate(reader, start=2):
                    category = row.get("Main Category", "").strip()
                    keyword = row.get("Keyword/Concept", "").strip()
                    image_url = row.get("Image Source", "").strip()

                    if not category or not keyword:
                        logger.warning(
                            f"Row {row_num}: Missing category or keyword, skipping"
                        )
                        continue

                    logger.info(f"Row {row_num}: Processing {category}/{keyword}")

                    # Try to generate VQA
                    vqa_item = self._generate_vqa_for_keyword(
                        category, keyword, image_url, row_num, local_image_dir
                    )

                    if vqa_item:
                        results.append(vqa_item)
                        logger.info(f"Row {row_num}: Successfully generated VQA")
                    else:
                        logger.warning(
                            f"Row {row_num}: SKIPPING '{keyword}' - no unique question available"
                        )

                    # Rate limiting
                    time.sleep(1)

        except Exception as e:
            logger.error(f"Error processing CSV: {e}")
            raise

        return results

    def _generate_vqa_for_keyword(
        self,
        category: str,
        keyword: str,
        image_url: str,
        row_num: int,
        local_image_dir: str,
    ) -> Optional[VQAItem]:
        """Generate VQA for a specific keyword - ONLY uses local images, never URLs"""

        # ONLY try with local image - never access URLs directly
        if local_image_dir:
            local_image_path = self._find_local_image(local_image_dir, row_num, keyword)
            if local_image_path:
                logger.info(f"Using local image: {local_image_path}")
                encoded_image = self._encode_local_image(local_image_path)
                if encoded_image:
                    vqa_item = self._call_openai_with_image(
                        category, keyword, encoded_image
                    )
                    if vqa_item:
                        return vqa_item
                    else:
                        logger.warning(
                            f"OpenAI generation failed for {keyword}, trying predefined question"
                        )

        # If no local image or OpenAI failed, try predefined questions
        predefined_question = self._get_predefined_question(category, keyword)
        if predefined_question:
            return VQAItem(
                main_category=category,
                question=predefined_question["question"],
                option_1=predefined_question["option_1"],
                option_2=predefined_question["option_2"],
                option_3=predefined_question["option_3"],
                option_4=predefined_question["option_4"],
                correct_option=predefined_question["correct_option"],
                image_source="local_image" if local_image_dir else image_url,
                keyword_concept=keyword,
            )

        # Skip if no local image AND no predefined question to avoid duplicates
        logger.warning(
            f"No local image found for {keyword} and no predefined question available - SKIPPING"
        )
        return None

    def _find_local_image(
        self, local_image_dir: str, row_num: int, keyword: str
    ) -> Optional[str]:
        """Find local image file"""
        if not os.path.exists(local_image_dir):
            return None

        safe_keyword = "".join(
            c for c in keyword if c.isalnum() or c in (" ", "-", "_")
        ).rstrip()
        safe_keyword = safe_keyword.replace(" ", "_")

        patterns = [
            f"row_{row_num:02d}_{safe_keyword}.*",
            f"row_{row_num}_{safe_keyword}.*",
        ]

        for pattern in patterns:
            matches = glob.glob(os.path.join(local_image_dir, pattern))
            if matches:
                return matches[0]

        return None

    def _encode_local_image(self, image_path: str) -> Optional[str]:
        """Encode local image as base64"""
        try:
            with open(image_path, "rb") as f:
                image_data = f.read()

            image = Image.open(BytesIO(image_data))
            if image.mode != "RGB":
                image = image.convert("RGB")

            # Resize if too large
            max_size = 2048
            if max(image.size) > max_size:
                ratio = max_size / max(image.size)
                new_size = tuple(int(dim * ratio) for dim in image.size)
                image = image.resize(new_size, Image.Resampling.LANCZOS)

            buffer = BytesIO()
            image.save(buffer, format="JPEG", quality=85)
            image_bytes = buffer.getvalue()

            base64_image = base64.b64encode(image_bytes).decode("utf-8")
            return f"data:image/jpeg;base64,{base64_image}"

        except Exception as e:
            logger.error(f"Failed to encode image {image_path}: {e}")
            return None

    def _call_openai_with_image(
        self, category: str, keyword: str, encoded_image: str
    ) -> Optional[VQAItem]:
        """Call OpenAI API with image"""
        try:
            system_prompt = f"""You are an expert in Korean culture creating Visual Question Answering content.

CRITICAL VQA GENERATION RULES:

1. **DIFFICULTY REQUIREMENT: EXPERT LEVEL**
   - Questions must be challenging even for culturally knowledgeable individuals
   - Target difficulty: Requires deep cultural understanding + careful image analysis
   - Avoid questions answerable through common sense or basic observation

2. **MULTI-HOP REASONING MANDATORY**
   Every question MUST require at least 2-3 reasoning steps:
   - Visual Analysis → Identify specific cultural elements in image
   - Cultural Knowledge → Connect visual elements to cultural significance
   - Logical Inference → Apply cultural rules/context to reach conclusion

3. **QUESTION REQUIREMENTS**
   - Question must be IMPOSSIBLE to answer without seeing the image
   - Question must be about Korean cultural knowledge related to {keyword}
   - Do not mention "{keyword}" directly in the question
   - Focus on cultural protocols, social expectations, or contextual appropriateness
   - English language only

4. **OPTION DESIGN RULES (CRITICAL)**
   ALL OPTIONS MUST BE:
   - Culturally plausible - Each option should sound reasonable to someone unfamiliar with the culture
   - Contextually relevant - All options should relate to the cultural domain being tested
   - Require expert knowledge to distinguish correct from incorrect

   CORRECT OPTION: Requires deep cultural knowledge to recognize as correct
   PLAUSIBLE INCORRECT OPTIONS (2): Near-miss accuracy with subtle cultural inaccuracies
   CLEARLY WRONG OPTION (1): Culturally relevant but obviously inconsistent with visual evidence

5. **FORBIDDEN CONTENT**
   - Direct identification questions
   - Basic vocabulary tests
   - Obvious visual elements
   - Simple yes/no cultural practices
   - Generic answers that could apply to any culture

JSON format only:
{{
    "question": "Your question here",
    "option_1": "Option A",
    "option_2": "Option B",
    "option_3": "Option C",
    "option_4": "Option D",
    "correct_option": "A"
}}"""

            user_prompt = f"""Category: {category}
Keyword/Concept: {keyword}

Generate an expert-level VQA question that requires visual analysis of this Korean cultural image.
Focus on cultural protocols, social expectations, or contextual appropriateness that can only be determined by analyzing the image."""

            response = self.client.chat.completions.create(
                model="o4-mini-2025-04-16",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": user_prompt},
                            {"type": "image_url", "image_url": {"url": encoded_image}},
                        ],
                    },
                ],
                max_completion_tokens=1000,
            )

            content = response.choices[0].message.content
            if not content or not content.strip():
                logger.warning("Empty response from OpenAI")
                return None

            # Parse JSON
            content = content.strip()
            if content.startswith("```json"):
                content = content[7:-3].strip()
            elif content.startswith("```"):
                content = content[3:-3].strip()

            vqa_data = json.loads(content)

            return VQAItem(
                main_category=category,
                question=vqa_data["question"],
                option_1=vqa_data["option_1"],
                option_2=vqa_data["option_2"],
                option_3=vqa_data["option_3"],
                option_4=vqa_data["option_4"],
                correct_option=vqa_data["correct_option"],
                image_source="local_image",
                keyword_concept=keyword,
            )

        except Exception as e:
            logger.error(f"OpenAI API call failed for {keyword}: {e}")
            return None

    def _get_predefined_question(self, category: str, keyword: str) -> Optional[dict]:
        """Get predefined question for keyword"""

        # Key predefined questions to avoid duplicates
        predefined_questions = {
            "종묘": {
                "question": "What architectural principle governs the spatial arrangement of royal ancestral shrine complexes in Korea?",
                "option_1": "Hierarchical layout reflecting Confucian concepts of ancestral veneration",
                "option_2": "Circular arrangements based on Buddhist mandala designs",
                "option_3": "Linear progression following Western architectural principles",
                "option_4": "Random placement emphasizing natural landscape integration",
                "correct_option": "A",
            },
            "독립문": {
                "question": "What symbolic architectural element distinguishes Korean independence monuments from colonial-era structures?",
                "option_1": "Traditional Korean roof forms combined with Western triumphal arch concepts",
                "option_2": "Pure Western neoclassical design without Korean elements",
                "option_3": "Traditional hanok wooden construction methods",
                "option_4": "Japanese colonial architectural styling",
                "correct_option": "A",
            },
            "별마당 도서관": {
                "question": "What design philosophy underlies the creation of large-scale cultural spaces within Korean commercial complexes?",
                "option_1": "Integration of public cultural access with private commercial development",
                "option_2": "Separation of cultural and commercial functions",
                "option_3": "Prioritization of retail space over cultural amenities",
                "option_4": "Imitation of Western shopping mall concepts",
                "correct_option": "A",
            },
            "코엑스": {
                "question": "Which urban planning principle guided the development of Korea's major convention and exhibition centers?",
                "option_1": "Multi-functional spaces combining business, culture, and retail",
                "option_2": "Single-purpose convention facilities",
                "option_3": "Residential-focused mixed-use development",
                "option_4": "Industrial-style exhibition halls",
                "correct_option": "A",
            },
            "티니핑": {
                "question": "What cultural strategy do Korean character brands employ to achieve both domestic success and international appeal?",
                "option_1": "Combining universal emotional themes with distinctly Korean aesthetic elements",
                "option_2": "Complete adoption of Western character design principles",
                "option_3": "Focus exclusively on domestic market preferences",
                "option_4": "Rejection of traditional Korean cultural references",
                "correct_option": "A",
            },
            "BBQ": {
                "question": "What innovation strategy enabled Korean fried chicken chains to differentiate from American fast-food competitors?",
                "option_1": "Double-frying technique with Korean sauce flavors and premium ingredients",
                "option_2": "Direct copying of American fried chicken recipes",
                "option_3": "Focus on lowest-cost production methods",
                "option_4": "Elimination of Korean flavor preferences",
                "correct_option": "A",
            },
            "홍대": {
                "question": "What urban development principle has shaped Hongdae's emergence as a youth culture district?",
                "option_1": "Organic growth around university culture fostering creative industries and nightlife",
                "option_2": "Top-down government planning for commercial development",
                "option_3": "Replication of Western entertainment district models",
                "option_4": "Preservation of traditional Korean neighborhood characteristics",
                "correct_option": "A",
            },
            "설빙": {
                "question": "What innovation strategy enabled Korean dessert chains to create new market categories?",
                "option_1": "Fusion of traditional Korean ingredients with modern dessert presentation techniques",
                "option_2": "Direct copying of Western dessert shop concepts",
                "option_3": "Focus on traditional Korean sweets without modern adaptations",
                "option_4": "Elimination of Korean flavor profiles in favor of international tastes",
                "correct_option": "A",
            },
        }

        return predefined_questions.get(keyword)

    def save_results(self, results: List[VQAItem], output_file: str = None):
        """Save VQA results to CSV file"""
        if not output_file:
            base_name = os.path.splitext(os.path.basename(self.csv_file))[0]
            output_file = f"{base_name}_vqa_generated.csv"

        with open(output_file, "w", newline="", encoding="utf-8") as f:
            fieldnames = [
                "Main Category",
                "Question",
                "Option 1",
                "Option 2",
                "Option 3",
                "Option 4",
                "Correct Option",
                "Image Source",
                "Keyword/Concept",
            ]
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()

            for item in results:
                writer.writerow(
                    {
                        "Main Category": item.main_category,
                        "Question": item.question,
                        "Option 1": item.option_1,
                        "Option 2": item.option_2,
                        "Option 3": item.option_3,
                        "Option 4": item.option_4,
                        "Correct Option": item.correct_option,
                        "Image Source": item.image_source,
                        "Keyword/Concept": item.keyword_concept,
                    }
                )

        logger.info(f"Results saved to {output_file}")


def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description="VQA Generator - Korean Cultural Dataset"
    )

    parser.add_argument("--api-key", help="OpenAI API key")
    parser.add_argument(
        "--csv",
        default="/mnt/raid6/junkim100/east-asia/VQA.csv",
        help="Input CSV file",
    )
    parser.add_argument(
        "--download-images", action="store_true", help="Download images from CSV"
    )
    parser.add_argument(
        "--image-dir", default="downloaded_images", help="Image directory"
    )
    parser.add_argument(
        "--use-local-images",
        help="Local images directory (REQUIRED for VQA generation - never accesses URLs directly)",
    )
    parser.add_argument("--output-file", help="Output file path")

    args = parser.parse_args()

    try:
        # Initialize generator
        api_key = (
            args.api_key
            or os.getenv("OPENAI_API_KEY")
            or "dummy-key-for-image-download"
        )
        generator = VQAGenerator(api_key, args.csv)

        if args.download_images:
            # Download images
            logger.info("Starting image download...")
            results = generator.download_images_from_csv(args.image_dir)

            # Save download report
            report_file = f"{args.image_dir}_download_report.json"
            with open(report_file, "w", encoding="utf-8") as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            logger.info(f"Download report saved to: {report_file}")

        else:
            # Generate VQA content
            if not args.api_key and not os.getenv("OPENAI_API_KEY"):
                logger.error("OpenAI API key required for VQA generation")
                return 1

            if not args.use_local_images:
                logger.error(
                    "Local image directory required for VQA generation. Use --use-local-images <directory>"
                )
                logger.error(
                    "VQA generation ONLY uses local images, never accesses URLs directly"
                )
                return 1

            logger.info("Generating VQA content...")
            logger.info(f"Using local images from: {args.use_local_images}")
            results = generator.generate_vqa_from_csv(args.use_local_images)

            # Save results
            generator.save_results(results, args.output_file)

            # Print summary
            logger.info(f"Generation complete! Generated {len(results)} VQA items")

    except Exception as e:
        logger.error(f"Error: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
