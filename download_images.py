#!/usr/bin/env python3
"""
Standalone Image Downloader for VQA Dataset

This script downloads all images from the VQA CSV file and saves them locally.
It handles various URL types including Notion URLs, Unsplash URLs, and provides
fallback placeholder images when original URLs are inaccessible.

Usage:
    python download_images.py
    python download_images.py --csv custom_data.csv --output-dir my_images
    python download_images.py --start-row 5 --end-row 10
"""

import argparse
import logging
import sys
import tempfile
import os

# Set up logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def main():
    """Main function for standalone image downloader"""
    parser = argparse.ArgumentParser(
        description="Download images from VQA CSV file",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python download_images.py
  python download_images.py --csv custom_data.csv --output-dir my_images
  python download_images.py --start-row 5 --end-row 10
  python download_images.py --output-dir korean_images --overwrite
        """,
    )

    parser.add_argument(
        "--csv",
        default="/mnt/raid6/junkim100/east-asia/VQA .csv",
        help="Path to input CSV file",
    )

    parser.add_argument(
        "--output-dir",
        default="downloaded_images",
        help="Directory to save downloaded images (default: downloaded_images)",
    )

    parser.add_argument(
        "--start-row", type=int, help="Start downloading from this row number"
    )

    parser.add_argument(
        "--end-row", type=int, help="End downloading at this row number"
    )

    parser.add_argument(
        "--overwrite", action="store_true", help="Overwrite existing images"
    )

    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be downloaded without actually downloading",
    )

    args = parser.parse_args()

    try:
        # Import VQAGenerator (we need a dummy API key for initialization)
        from vqa_generator import VQAGenerator

        # Create a temporary rules file since we only need the downloader
        with tempfile.NamedTemporaryFile(mode="w", suffix=".md", delete=False) as f:
            f.write("# Temporary rules file for image download")
            rules_file = f.name

        try:
            # Initialize generator with dummy API key (note: parameters are api_key, rules_file, csv_file)
            generator = VQAGenerator("dummy-key", rules_file, args.csv)

            if args.dry_run:
                logger.info("DRY RUN MODE - No images will be downloaded")
                # Show what would be downloaded
                results = generator._preview_image_downloads(
                    args.output_dir, args.start_row, args.end_row
                )

                print(f"\n=== DRY RUN SUMMARY ===")
                print(f"Would download: {results['downloadable']} images")
                print(f"Would skip: {results['skippable']} images")
                print(f"Total rows: {results['total']} rows")

            else:
                # Perform actual download
                logger.info(f"Starting image download to: {args.output_dir}")

                # Modify the download method to handle row ranges
                download_results = generator._download_images_with_range(
                    args.output_dir, args.start_row, args.end_row, args.overwrite
                )

                # Save download report
                report_file = f"{args.output_dir}_download_report.json"
                import json

                with open(report_file, "w", encoding="utf-8") as f:
                    json.dump(download_results, f, ensure_ascii=False, indent=2)

                print(f"\n=== DOWNLOAD COMPLETE ===")
                print(f"Successful: {len(download_results['successful'])}")
                print(f"Failed: {len(download_results['failed'])}")
                print(f"Skipped: {len(download_results['skipped'])}")
                print(f"Report saved to: {report_file}")

        finally:
            # Clean up temporary rules file
            os.unlink(rules_file)

        return 0

    except ImportError:
        logger.error(
            "Could not import VQAGenerator. Make sure vqa_generator.py is in the same directory."
        )
        return 1
    except Exception as e:
        logger.error(f"Error during image download: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
