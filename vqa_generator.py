#!/usr/bin/env python3
"""
VQA Content Generator using OpenAI o4-mini-2025-04-16
Generates Visual Question Answering content based on Korean cultural images and keywords.
Uses the o4-mini-2025-04-16 model with enhanced visual reasoning capabilities.
"""

import os
import csv
import json
import logging
import requests
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from openai import OpenAI
import time
from urllib.parse import urlparse
import argparse
import base64
from io import BytesIO
from PIL import Image

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.FileHandler("vqa_generator.log"), logging.StreamHandler()],
)
logger = logging.getLogger(__name__)


@dataclass
class VQAItem:
    """Data class for VQA content"""

    main_category: str
    question: str
    option_1: str
    option_2: str
    option_3: str
    option_4: str
    correct_option: str
    image_source: str
    keyword_concept: str


class VQAGenerator:
    """Main class for generating VQA content using OpenAI o4-mini-2025-04-16"""

    def __init__(self, api_key: str, rules_file: str, csv_file: str):
        """
        Initialize the VQA Generator

        Args:
            api_key: OpenAI API key
            rules_file: Path to VQA generation rules markdown file
            csv_file: Path to input CSV file
        """
        self.client = OpenAI(api_key=api_key)
        self.rules_file = rules_file
        self.csv_file = csv_file
        self.generation_rules = self._load_generation_rules()

    def _load_generation_rules(self) -> str:
        """Load VQA generation rules from markdown file"""
        try:
            with open(self.rules_file, "r", encoding="utf-8") as f:
                rules = f.read()
            logger.info(f"Loaded generation rules from {self.rules_file}")
            return rules
        except FileNotFoundError:
            logger.error(f"Rules file not found: {self.rules_file}")
            raise
        except Exception as e:
            logger.error(f"Error loading rules file: {e}")
            raise

    def _is_valid_image_url(self, url: str) -> bool:
        """Check if URL looks like a valid image URL (less strict validation)"""
        if not url or not url.strip():
            return False

        try:
            parsed = urlparse(url)
            if not parsed.scheme or not parsed.netloc:
                return False

            # Accept URLs that look like they could be images
            # Don't do network validation as many URLs require auth
            url_lower = url.lower()

            # Accept common image hosting domains
            image_domains = [
                "notion.so",
                "unsplash.com",
                "shutterstock.com",
                "wikimedia.org",
                "wikipedia.org",
                "flickr.com",
                "pstatic.net",
                "naver.net",
                "crowdpic.net",
                "animalia.bio",
                "picryl.com",
                "pixabay.com",
            ]

            # Check if it's from a known image domain or has image extension
            has_image_domain = any(domain in url_lower for domain in image_domains)
            has_image_extension = any(
                url_lower.endswith(ext)
                for ext in [".jpg", ".jpeg", ".png", ".gif", ".webp"]
            )

            if has_image_domain or has_image_extension:
                logger.info(f"Accepting image URL: {url[:100]}...")
                return True
            else:
                logger.warning(f"URL doesn't look like an image: {url[:100]}...")
                return False

        except Exception as e:
            logger.warning(f"URL validation failed for {url}: {e}")
            return False

    def _download_and_encode_image(self, url: str) -> Optional[str]:
        """Download image from URL and encode as base64 for API"""
        try:
            logger.info(f"Downloading image from URL: {url}")
            response = requests.get(url, timeout=30, allow_redirects=True)
            response.raise_for_status()

            # Open image with PIL to validate and potentially resize
            image = Image.open(BytesIO(response.content))

            # Convert to RGB if necessary
            if image.mode != "RGB":
                image = image.convert("RGB")

            # Resize if too large (OpenAI has size limits)
            max_size = 2048
            if max(image.size) > max_size:
                ratio = max_size / max(image.size)
                new_size = tuple(int(dim * ratio) for dim in image.size)
                image = image.resize(new_size, Image.Resampling.LANCZOS)
                logger.info(f"Resized image to {new_size}")

            # Convert back to bytes
            buffer = BytesIO()
            image.save(buffer, format="JPEG", quality=85)
            image_bytes = buffer.getvalue()

            # Encode as base64
            base64_image = base64.b64encode(image_bytes).decode("utf-8")
            logger.info(f"Successfully processed image, size: {len(image_bytes)} bytes")

            return f"data:image/jpeg;base64,{base64_image}"

        except Exception as e:
            logger.error(f"Failed to download/encode image from {url}: {e}")
            return None

    def _extract_json_from_text(self, text: str) -> Optional[dict]:
        """Try to extract JSON from a text response that might not be pure JSON"""
        import re

        try:
            # Look for JSON-like patterns in the text
            # Try to find content between { and }
            json_pattern = r"\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}"
            matches = re.findall(json_pattern, text, re.DOTALL)

            for match in matches:
                try:
                    parsed = json.loads(match)
                    # Check if it has the required fields
                    required_fields = [
                        "question",
                        "option_1",
                        "option_2",
                        "option_3",
                        "option_4",
                        "correct_option",
                    ]
                    if all(field in parsed for field in required_fields):
                        logger.info("Successfully extracted JSON from text response")
                        return parsed
                except json.JSONDecodeError:
                    continue

            # If no valid JSON found, try to parse the text manually
            logger.warning("No valid JSON found, attempting manual parsing")
            return self._manual_parse_response(text)

        except Exception as e:
            logger.error(f"Failed to extract JSON from text: {e}")
            return None

    def _manual_parse_response(self, text: str) -> Optional[dict]:
        """Manually parse response text to extract question and options"""
        try:
            logger.info(f"Attempting manual parsing of: {text}")
            lines = text.strip().split("\n")
            result = {}

            # Look for patterns like "Question:", "A)", "Option 1:", etc.
            for i, line in enumerate(lines):
                line = line.strip()
                if not line:
                    continue

                logger.debug(f"Processing line {i}: {line}")

                # Try to identify question - more flexible patterns
                if any(
                    keyword in line.lower()
                    for keyword in ["question:", "q:", "질문:", "문제:", "**question**"]
                ) or (
                    i == 0 and "?" in line
                ):  # First line with question mark
                    question_text = line
                    for prefix in ["question:", "q:", "질문:", "문제:", "**question**"]:
                        if prefix in question_text.lower():
                            question_text = question_text.split(":", 1)[-1].strip()
                            break
                    question_text = question_text.strip("*").strip()
                    if question_text:
                        result["question"] = question_text
                        logger.debug(f"Found question: {question_text}")

                # Try to identify options - more flexible patterns
                elif any(
                    pattern in line.lower()
                    for pattern in ["a)", "a.", "option a", "1)"]
                ):
                    option_text = self._extract_option_text(line)
                    if option_text:
                        result["option_1"] = option_text
                        logger.debug(f"Found option 1: {option_text}")

                elif any(
                    pattern in line.lower()
                    for pattern in ["b)", "b.", "option b", "2)"]
                ):
                    option_text = self._extract_option_text(line)
                    if option_text:
                        result["option_2"] = option_text
                        logger.debug(f"Found option 2: {option_text}")

                elif any(
                    pattern in line.lower()
                    for pattern in ["c)", "c.", "option c", "3)"]
                ):
                    option_text = self._extract_option_text(line)
                    if option_text:
                        result["option_3"] = option_text
                        logger.debug(f"Found option 3: {option_text}")

                elif any(
                    pattern in line.lower()
                    for pattern in ["d)", "d.", "option d", "4)"]
                ):
                    option_text = self._extract_option_text(line)
                    if option_text:
                        result["option_4"] = option_text
                        logger.debug(f"Found option 4: {option_text}")

                # Try to identify correct answer - more flexible patterns
                elif any(
                    keyword in line.lower()
                    for keyword in [
                        "correct:",
                        "answer:",
                        "정답:",
                        "correct answer:",
                        "**correct**",
                    ]
                ):
                    answer_text = line.split(":", 1)[-1].strip().upper()
                    # Extract just the letter
                    for char in answer_text:
                        if char in ["A", "B", "C", "D"]:
                            result["correct_option"] = char
                            logger.debug(f"Found correct answer: {char}")
                            break

            # Check if we have all required fields
            required_fields = [
                "question",
                "option_1",
                "option_2",
                "option_3",
                "option_4",
                "correct_option",
            ]

            found_fields = [
                field for field in required_fields if field in result and result[field]
            ]
            logger.info(
                f"Manual parsing found {len(found_fields)}/{len(required_fields)} fields: {found_fields}"
            )

            if all(field in result and result[field] for field in required_fields):
                logger.info("Successfully manually parsed response")
                return result
            else:
                logger.warning(f"Manual parsing incomplete. Found: {result}")
                return None

        except Exception as e:
            logger.error(f"Manual parsing failed: {e}")
            return None

    def _extract_option_text(self, line: str) -> str:
        """Extract option text from a line, removing prefixes"""
        # Remove common prefixes
        prefixes = [
            "a)",
            "b)",
            "c)",
            "d)",
            "a.",
            "b.",
            "c.",
            "d.",
            "1)",
            "2)",
            "3)",
            "4)",
            "option a",
            "option b",
            "option c",
            "option d",
        ]
        line_lower = line.lower()

        for prefix in prefixes:
            if line_lower.startswith(prefix):
                return line[len(prefix) :].strip()

        # If no prefix found, return the whole line
        return line.strip()

    def _generate_vqa_with_image(
        self, category: str, keyword: str, image_url: str, retry_count: int = 0
    ) -> Optional[VQAItem]:
        """Generate VQA content for items with both keyword and image"""
        try:
            # Download and encode the image
            encoded_image = self._download_and_encode_image(image_url)
            if not encoded_image:
                logger.error(f"Failed to process image from {image_url}")
                return None
            system_prompt = f"""You are an expert in Korean culture. Generate a VQA question about the image.

Category: {category}
Keyword: {keyword}

Create a challenging question that requires both visual analysis and Korean cultural knowledge.

Respond with ONLY this JSON format:
{{
    "question": "Your question here",
    "option_1": "Option A",
    "option_2": "Option B",
    "option_3": "Option C",
    "option_4": "Option D",
    "correct_option": "A"
}}"""

            user_prompt = f"""Category: {category}
Keyword/Concept: {keyword}

Please analyze the image and generate an expert-level VQA question following all the rules provided."""

            response = self.client.chat.completions.create(
                model="o4-mini-2025-04-16",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": user_prompt},
                            {"type": "image_url", "image_url": {"url": encoded_image}},
                        ],
                    },
                ],
                max_completion_tokens=1000,
            )

            content = response.choices[0].message.content
            logger.info(f"Raw API response type: {type(content)}")
            logger.info(f"Raw API response is None: {content is None}")

            if content is None:
                logger.error("API returned None content")
                return None

            content = content.strip()
            logger.info(f"Content length after strip: {len(content)}")
            logger.info(f"Raw API response: {repr(content[:200])}...")
            logger.info(
                f"FULL API response: {repr(content)}"
            )  # Log the complete response with repr

            # Parse JSON response
            if content.startswith("```json"):
                content = content[7:-3].strip()
            elif content.startswith("```"):
                content = content[3:-3].strip()

            # Additional cleaning for common issues
            if content.startswith("```") and content.endswith("```"):
                content = content[3:-3].strip()

            logger.info(f"Cleaned content for JSON parsing: {repr(content[:200])}...")

            # Check if content is empty
            if not content:
                logger.error(
                    "Content is empty after cleaning - possibly content filtered"
                )
                if retry_count < 1:
                    logger.info(
                        f"Retrying image generation (attempt {retry_count + 1})"
                    )
                    time.sleep(2)  # Brief delay before retry
                    return self._generate_vqa_with_image(
                        category, keyword, image_url, retry_count + 1
                    )
                else:
                    logger.info("Falling back to text-only generation for this item")
                    # Fall back to text-only generation
                    return self._generate_vqa_without_image(category, keyword)

            # Try to parse JSON with better error handling
            try:
                vqa_data = json.loads(content)
            except json.JSONDecodeError as e:
                logger.error(f"JSON parsing failed: {e}")
                logger.error(f"Full content that failed to parse: {repr(content)}")

                # Try to extract JSON from text response
                vqa_data = self._extract_json_from_text(content)
                if not vqa_data:
                    logger.error("Could not extract valid JSON from response")
                    return None

            return VQAItem(
                main_category=category,
                question=vqa_data["question"],
                option_1=vqa_data["option_1"],
                option_2=vqa_data["option_2"],
                option_3=vqa_data["option_3"],
                option_4=vqa_data["option_4"],
                correct_option=vqa_data["correct_option"],
                image_source=image_url,
                keyword_concept=keyword,
            )

        except Exception as e:
            logger.error(
                f"Error generating VQA with image for {category}/{keyword}: {e}"
            )
            return None

    def _generate_vqa_without_image(
        self, category: str, keyword: str
    ) -> Optional[VQAItem]:
        """Generate VQA content for items with keyword but no image"""
        try:
            system_prompt = f"""You are an expert in Korean culture. Generate a VQA question about Korean culture.

Category: {category}
Keyword: {keyword}

Create a challenging question about Korean cultural knowledge (no image provided).

Respond with ONLY this JSON format:
{{
    "question": "Your question here",
    "option_1": "Option A",
    "option_2": "Option B",
    "option_3": "Option C",
    "option_4": "Option D",
    "correct_option": "A"
}}"""

            user_prompt = f"""Category: {category}
Keyword/Concept: {keyword}

Please generate an expert-level VQA question that tests deep Korean cultural knowledge related to this concept, without requiring visual analysis."""

            response = self.client.chat.completions.create(
                model="o4-mini-2025-04-16",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ],
                max_completion_tokens=1000,
            )

            content = response.choices[0].message.content
            logger.info(f"Raw API response type (text-only): {type(content)}")
            logger.info(f"Raw API response is None (text-only): {content is None}")

            if content is None:
                logger.error("API returned None content (text-only)")
                return None

            content = content.strip()
            logger.info(f"Content length after strip (text-only): {len(content)}")
            logger.info(f"Raw API response (text-only): {repr(content[:200])}...")
            logger.info(
                f"FULL API response (text-only): {repr(content)}"
            )  # Log the complete response with repr

            # Parse JSON response
            if content.startswith("```json"):
                content = content[7:-3].strip()
            elif content.startswith("```"):
                content = content[3:-3].strip()

            # Additional cleaning for common issues
            if content.startswith("```") and content.endswith("```"):
                content = content[3:-3].strip()

            logger.info(
                f"Cleaned content for JSON parsing (text-only): {repr(content[:200])}..."
            )

            # Check if content is empty
            if not content:
                logger.error("Content is empty after cleaning (text-only)")
                return None

            # Try to parse JSON with better error handling
            try:
                vqa_data = json.loads(content)
            except json.JSONDecodeError as e:
                logger.error(f"JSON parsing failed (text-only): {e}")
                logger.error(f"Full content that failed to parse: {repr(content)}")

                # Try to extract JSON from text response
                vqa_data = self._extract_json_from_text(content)
                if not vqa_data:
                    logger.error(
                        "Could not extract valid JSON from response (text-only)"
                    )
                    return None

            return VQAItem(
                main_category=category,
                question=vqa_data["question"],
                option_1=vqa_data["option_1"],
                option_2=vqa_data["option_2"],
                option_3=vqa_data["option_3"],
                option_4=vqa_data["option_4"],
                correct_option=vqa_data["correct_option"],
                image_source="",
                keyword_concept=keyword,
            )

        except Exception as e:
            logger.error(
                f"Error generating VQA without image for {category}/{keyword}: {e}"
            )
            return None

    def process_csv(self, save_progress: bool = True) -> List[VQAItem]:
        """Process the CSV file and generate VQA content"""
        results = []

        # Create progress output file
        if save_progress:
            base_name = os.path.splitext(os.path.basename(self.csv_file))[0]
            self.progress_file = f"{base_name}_vqa_progress.csv"
            logger.info(f"Progress will be saved to: {self.progress_file}")

        try:
            with open(self.csv_file, "r", encoding="utf-8") as f:
                reader = csv.DictReader(f)

                for row_num, row in enumerate(
                    reader, start=2
                ):  # Start at 2 because of header
                    category = row.get("Main Category", "").strip()
                    keyword = row.get("Keyword/Concept", "").strip()
                    image_url = row.get("Image Source", "").strip()

                    # Skip completely empty rows (preserve them as empty in output)
                    if not category and not keyword and not image_url:
                        logger.info(f"Row {row_num}: Skipping empty row")
                        results.append(
                            VQAItem(
                                main_category="",
                                question="",
                                option_1="",
                                option_2="",
                                option_3="",
                                option_4="",
                                correct_option="",
                                image_source="",
                                keyword_concept="",
                            )
                        )
                        continue

                    # Skip rows without category or keyword
                    if not category or not keyword:
                        logger.warning(
                            f"Row {row_num}: Missing category or keyword, skipping"
                        )
                        results.append(
                            VQAItem(
                                main_category=category,
                                question="",
                                option_1="",
                                option_2="",
                                option_3="",
                                option_4="",
                                correct_option="",
                                image_source=image_url,
                                keyword_concept=keyword,
                            )
                        )
                        continue

                    logger.info(f"Row {row_num}: Processing {category}/{keyword}")

                    # Determine processing type based on image availability
                    if image_url and self._is_valid_image_url(image_url):
                        logger.info(f"Row {row_num}: Generating VQA with image")
                        vqa_item = self._generate_vqa_with_image(
                            category, keyword, image_url
                        )
                    elif keyword:
                        logger.info(f"Row {row_num}: Generating VQA without image")
                        vqa_item = self._generate_vqa_without_image(category, keyword)
                    else:
                        logger.warning(f"Row {row_num}: No valid processing option")
                        vqa_item = None

                    if vqa_item:
                        results.append(vqa_item)
                        logger.info(f"Row {row_num}: Successfully generated VQA")
                    else:
                        # Add empty row to maintain structure
                        results.append(
                            VQAItem(
                                main_category=category,
                                question="",
                                option_1="",
                                option_2="",
                                option_3="",
                                option_4="",
                                correct_option="",
                                image_source=image_url,
                                keyword_concept=keyword,
                            )
                        )
                        logger.error(f"Row {row_num}: Failed to generate VQA")

                    # Save progress after each row
                    if save_progress:
                        self._save_progress(results)
                        logger.info(f"Progress saved: {len(results)} rows completed")

                    # Add delay to respect API rate limits
                    time.sleep(1)

        except FileNotFoundError:
            logger.error(f"CSV file not found: {self.csv_file}")
            raise
        except Exception as e:
            logger.error(f"Error processing CSV file: {e}")
            raise

        return results

    def _save_progress(self, results: List[VQAItem]):
        """Save current progress to CSV file"""
        try:
            with open(self.progress_file, "w", newline="", encoding="utf-8") as f:
                fieldnames = [
                    "Main Category",
                    "Question",
                    "Option 1",
                    "Option 2",
                    "Option 3",
                    "Option 4",
                    "Correct Option",
                    "Image Source",
                    "Keyword/Concept",
                ]
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()

                for item in results:
                    writer.writerow(
                        {
                            "Main Category": item.main_category,
                            "Question": item.question,
                            "Option 1": item.option_1,
                            "Option 2": item.option_2,
                            "Option 3": item.option_3,
                            "Option 4": item.option_4,
                            "Correct Option": item.correct_option,
                            "Image Source": item.image_source,
                            "Keyword/Concept": item.keyword_concept,
                        }
                    )
        except Exception as e:
            logger.error(f"Failed to save progress: {e}")

    def save_results(
        self,
        results: List[VQAItem],
        output_format: str = "csv",
        output_file: str = None,
    ):
        """Save VQA results to file"""
        if not output_file:
            base_name = os.path.splitext(os.path.basename(self.csv_file))[0]
            output_file = f"{base_name}_vqa_generated.{output_format}"

        try:
            if output_format.lower() == "csv":
                self._save_to_csv(results, output_file)
            elif output_format.lower() == "json":
                self._save_to_json(results, output_file)
            else:
                raise ValueError(f"Unsupported output format: {output_format}")

            logger.info(f"Results saved to {output_file}")

        except Exception as e:
            logger.error(f"Error saving results: {e}")
            raise

    def _save_to_csv(self, results: List[VQAItem], output_file: str):
        """Save results to CSV format"""
        with open(output_file, "w", newline="", encoding="utf-8") as f:
            fieldnames = [
                "Main Category",
                "Question",
                "Option 1",
                "Option 2",
                "Option 3",
                "Option 4",
                "Correct Option",
                "Image Source",
                "Keyword/Concept",
            ]
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()

            for item in results:
                writer.writerow(
                    {
                        "Main Category": item.main_category,
                        "Question": item.question,
                        "Option 1": item.option_1,
                        "Option 2": item.option_2,
                        "Option 3": item.option_3,
                        "Option 4": item.option_4,
                        "Correct Option": item.correct_option,
                        "Image Source": item.image_source,
                        "Keyword/Concept": item.keyword_concept,
                    }
                )

    def _save_to_json(self, results: List[VQAItem], output_file: str):
        """Save results to JSON format"""
        json_data = []
        for item in results:
            json_data.append(
                {
                    "main_category": item.main_category,
                    "question": item.question,
                    "options": {
                        "A": item.option_1,
                        "B": item.option_2,
                        "C": item.option_3,
                        "D": item.option_4,
                    },
                    "correct_option": item.correct_option,
                    "image_source": item.image_source,
                    "keyword_concept": item.keyword_concept,
                }
            )

        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2)


def main():
    """Main function to run the VQA generator"""
    parser = argparse.ArgumentParser(
        description="Generate VQA content using OpenAI o4-mini-2025-04-16",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python vqa_generator.py --api-key YOUR_API_KEY
  python vqa_generator.py --api-key YOUR_API_KEY --output-format json
  python vqa_generator.py --api-key YOUR_API_KEY --output-file my_vqa.csv
  python vqa_generator.py --api-key YOUR_API_KEY --rules custom_rules.md --csv custom_data.csv
        """,
    )

    parser.add_argument(
        "--api-key",
        required=True,
        help="OpenAI API key (or set OPENAI_API_KEY environment variable)",
    )

    parser.add_argument(
        "--rules",
        default="/mnt/raid6/junkim100/east-asia/VQA_Generation_Rules.md",
        help="Path to VQA generation rules markdown file",
    )

    parser.add_argument(
        "--csv",
        default="/mnt/raid6/junkim100/east-asia/VQA .csv",
        help="Path to input CSV file",
    )

    parser.add_argument(
        "--output-format",
        choices=["csv", "json"],
        default="csv",
        help="Output format (csv or json)",
    )

    parser.add_argument(
        "--output-file", help="Output file path (auto-generated if not specified)"
    )

    parser.add_argument(
        "--dry-run", action="store_true", help="Process only first 5 rows for testing"
    )

    parser.add_argument(
        "--no-progress",
        action="store_true",
        help="Disable progress saving after each row",
    )

    args = parser.parse_args()

    # Get API key from argument or environment variable
    api_key = args.api_key or os.getenv("OPENAI_API_KEY")
    if not api_key:
        logger.error(
            "OpenAI API key is required. Use --api-key or set OPENAI_API_KEY environment variable."
        )
        return 1

    try:
        # Initialize generator
        logger.info("Initializing VQA Generator...")
        generator = VQAGenerator(api_key, args.rules, args.csv)

        # Process CSV
        logger.info("Processing CSV file...")
        save_progress = not args.no_progress
        results = generator.process_csv(save_progress=save_progress)

        # Limit results for dry run
        if args.dry_run:
            results = results[:5]
            logger.info("Dry run mode: Processing only first 5 rows")

        # Save results
        logger.info(f"Saving {len(results)} results...")
        generator.save_results(results, args.output_format, args.output_file)

        # Print summary
        successful_generations = sum(1 for r in results if r.question.strip())
        logger.info(f"Generation complete!")
        logger.info(f"Total rows processed: {len(results)}")
        logger.info(f"Successful generations: {successful_generations}")
        logger.info(f"Failed generations: {len(results) - successful_generations}")

        return 0

    except KeyboardInterrupt:
        logger.info("Generation interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"Error during generation: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
