#!/usr/bin/env python3
"""
VQA Content Generator using OpenAI o4-mini-2025-04-16
Generates Visual Question Answering content based on Korean cultural images and keywords.
Uses the o4-mini-2025-04-16 model with enhanced visual reasoning capabilities.
"""

import os
import csv
import json
import logging
import requests
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from openai import OpenAI
import time
from urllib.parse import urlparse
import argparse
import base64
from io import BytesIO
from PIL import Image

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.FileHandler("vqa_generator.log"), logging.StreamHandler()],
)
logger = logging.getLogger(__name__)


@dataclass
class VQAItem:
    """Data class for VQA content"""

    main_category: str
    question: str
    option_1: str
    option_2: str
    option_3: str
    option_4: str
    correct_option: str
    image_source: str
    keyword_concept: str


class VQAGenerator:
    """Main class for generating VQA content using OpenAI o4-mini-2025-04-16"""

    def __init__(self, api_key: str, rules_file: str, csv_file: str):
        """
        Initialize the VQA Generator

        Args:
            api_key: OpenAI API key
            rules_file: Path to VQA generation rules markdown file
            csv_file: Path to input CSV file
        """
        # Initialize OpenAI client (only if API key is provided)
        self.client = OpenAI(api_key=api_key) if api_key else None
        self.rules_file = rules_file
        self.csv_file = csv_file
        self.generation_rules = self._load_generation_rules()

    def _load_generation_rules(self) -> str:
        """Load VQA generation rules from markdown file"""
        try:
            with open(self.rules_file, "r", encoding="utf-8") as f:
                rules = f.read()
            logger.info(f"Loaded generation rules from {self.rules_file}")
            return rules
        except FileNotFoundError:
            logger.error(f"Rules file not found: {self.rules_file}")
            raise
        except Exception as e:
            logger.error(f"Error loading rules file: {e}")
            raise

    def _is_valid_image_url(self, url: str) -> bool:
        """Check if URL looks like a valid image URL (less strict validation)"""
        if not url or not url.strip():
            return False

        try:
            parsed = urlparse(url)
            if not parsed.scheme or not parsed.netloc:
                return False

            # Accept URLs that look like they could be images
            # Don't do network validation as many URLs require auth
            url_lower = url.lower()

            # Accept common image hosting domains
            image_domains = [
                "notion.so",
                "unsplash.com",
                "shutterstock.com",
                "wikimedia.org",
                "wikipedia.org",
                "flickr.com",
                "pstatic.net",
                "naver.net",
                "crowdpic.net",
                "animalia.bio",
                "picryl.com",
                "pixabay.com",
            ]

            # Check if it's from a known image domain or has image extension
            has_image_domain = any(domain in url_lower for domain in image_domains)
            has_image_extension = any(
                url_lower.endswith(ext)
                for ext in [".jpg", ".jpeg", ".png", ".gif", ".webp"]
            )

            if has_image_domain or has_image_extension:
                logger.info(f"Accepting image URL: {url[:100]}...")
                return True
            else:
                logger.warning(f"URL doesn't look like an image: {url[:100]}...")
                return False

        except Exception as e:
            logger.warning(f"URL validation failed for {url}: {e}")
            return False

    def _download_and_encode_image(self, url: str) -> Optional[str]:
        """Download image from URL and encode as base64 for API"""
        try:
            logger.info(f"Downloading image from URL: {url}")

            # Set up headers to mimic a browser request
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                "Accept": "image/webp,image/apng,image/*,*/*;q=0.8",
                "Accept-Language": "en-US,en;q=0.9",
                "Accept-Encoding": "gzip, deflate, br",
                "DNT": "1",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1",
            }

            # Try multiple download strategies
            strategies = [
                # Strategy 1: Direct download with headers
                lambda: requests.get(
                    url, headers=headers, timeout=30, allow_redirects=True
                ),
                # Strategy 2: Session-based download
                lambda: self._session_download(url, headers),
                # Strategy 3: Alternative image sources
                lambda: self._find_alternative_image(url),
            ]

            response = None
            for i, strategy in enumerate(strategies):
                try:
                    logger.info(f"Trying download strategy {i+1}")
                    response = strategy()
                    if response and response.status_code == 200:
                        break
                except Exception as e:
                    logger.warning(f"Strategy {i+1} failed: {e}")
                    continue

            if not response or response.status_code != 200:
                logger.error(f"All download strategies failed for {url}")
                # Try to get a placeholder image for this concept
                placeholder_url = self._get_placeholder_image_for_concept(
                    url.split("/")[-1] if "/" in url else "korean"
                )
                if placeholder_url:
                    logger.info(f"Trying placeholder image: {placeholder_url}")
                    try:
                        response = requests.get(
                            placeholder_url, timeout=30, allow_redirects=True
                        )
                        if response.status_code == 200:
                            logger.info("Successfully got placeholder image")
                        else:
                            return None
                    except Exception as e:
                        logger.warning(f"Placeholder image failed: {e}")
                        return None
                else:
                    return None

            # Open image with PIL to validate and potentially resize
            image = Image.open(BytesIO(response.content))

            # Convert to RGB if necessary
            if image.mode != "RGB":
                image = image.convert("RGB")

            # Resize if too large (OpenAI has size limits)
            max_size = 2048
            if max(image.size) > max_size:
                ratio = max_size / max(image.size)
                new_size = tuple(int(dim * ratio) for dim in image.size)
                image = image.resize(new_size, Image.Resampling.LANCZOS)
                logger.info(f"Resized image to {new_size}")

            # Convert back to bytes
            buffer = BytesIO()
            image.save(buffer, format="JPEG", quality=85)
            image_bytes = buffer.getvalue()

            # Encode as base64
            base64_image = base64.b64encode(image_bytes).decode("utf-8")
            logger.info(f"Successfully processed image, size: {len(image_bytes)} bytes")

            return f"data:image/jpeg;base64,{base64_image}"

        except Exception as e:
            logger.error(f"Failed to download/encode image from {url}: {e}")
            return None

    def _session_download(self, url: str, headers: dict) -> requests.Response:
        """Try downloading with a session"""
        session = requests.Session()
        session.headers.update(headers)
        return session.get(url, timeout=30, allow_redirects=True)

    def _find_alternative_image(self, url: str) -> Optional[requests.Response]:
        """Try to find alternative image sources for problematic URLs"""
        # For Google redirect URLs, try to extract the actual image URL
        if "google.com/url" in url and "url=" in url:
            try:
                import urllib.parse

                parsed = urllib.parse.parse_qs(urllib.parse.urlparse(url).query)
                if "url" in parsed:
                    actual_url = parsed["url"][0]
                    logger.info(
                        f"Extracted actual URL from Google redirect: {actual_url}"
                    )
                    return requests.get(actual_url, timeout=30, allow_redirects=True)
            except Exception as e:
                logger.warning(f"Failed to extract URL from Google redirect: {e}")

        # For Notion URLs, try different approaches
        if "notion.so" in url:
            # Try removing authentication parameters
            try:
                base_url = url.split("?")[0]
                logger.info(f"Trying Notion URL without parameters: {base_url}")
                return requests.get(base_url, timeout=30, allow_redirects=True)
            except Exception as e:
                logger.warning(f"Failed to access Notion URL without parameters: {e}")

        return None

    def _get_placeholder_image_for_concept(self, keyword: str) -> Optional[str]:
        """Get a placeholder image URL for a given concept from free sources"""
        # Map keywords to search terms for free image sources
        search_mappings = {
            "제주 돌집": "traditional korean stone house",
            "월정교": "korean traditional bridge",
            "운현궁": "korean palace architecture",
            "명동": "seoul shopping district",
            "남산타워": "seoul tower",
            "신라대종": "korean traditional bell",
            "고려대학교": "korean university architecture",
            "한강다리": "seoul bridge han river",
            "탑골공원": "korean traditional pagoda",
            "광화문": "korean palace gate",
            "떡집": "korean traditional shop",
            "한옥": "korean traditional house",
        }

        search_term = search_mappings.get(keyword, f"korean traditional {keyword}")

        # Try Unsplash with search term
        try:
            unsplash_search_url = (
                f"https://source.unsplash.com/800x600/?{search_term.replace(' ', ',')}"
            )
            logger.info(f"Trying Unsplash placeholder: {unsplash_search_url}")
            response = requests.get(
                unsplash_search_url, timeout=30, allow_redirects=True
            )
            if response.status_code == 200:
                return response.url  # Unsplash redirects to actual image
        except Exception as e:
            logger.warning(f"Unsplash placeholder failed: {e}")

        return None

    def download_images_from_csv(self, output_dir: str = "downloaded_images") -> dict:
        """Download all images from the CSV file and save them locally"""
        import os
        from urllib.parse import urlparse

        # Create output directory
        os.makedirs(output_dir, exist_ok=True)

        download_results = {"successful": [], "failed": [], "skipped": []}

        logger.info(f"Starting image download to directory: {output_dir}")

        try:
            with open(self.csv_file, "r", encoding="utf-8") as f:
                reader = csv.DictReader(f)

                for row_num, row in enumerate(reader, start=2):
                    category = row.get("Main Category", "").strip()
                    keyword = row.get("Keyword/Concept", "").strip()
                    image_url = row.get("Image Source", "").strip()

                    if not image_url or not self._is_valid_image_url(image_url):
                        logger.info(f"Row {row_num}: Skipping - no valid image URL")
                        download_results["skipped"].append(
                            {
                                "row": row_num,
                                "keyword": keyword,
                                "reason": "No valid image URL",
                            }
                        )
                        continue

                    # Generate filename
                    safe_keyword = "".join(
                        c for c in keyword if c.isalnum() or c in (" ", "-", "_")
                    ).rstrip()
                    safe_keyword = safe_keyword.replace(" ", "_")

                    # Get file extension from URL
                    parsed_url = urlparse(image_url)
                    path = parsed_url.path.lower()
                    if path.endswith((".jpg", ".jpeg")):
                        ext = ".jpg"
                    elif path.endswith(".png"):
                        ext = ".png"
                    elif path.endswith(".webp"):
                        ext = ".webp"
                    else:
                        ext = ".jpg"  # Default

                    filename = f"row_{row_num:02d}_{safe_keyword}{ext}"
                    filepath = os.path.join(output_dir, filename)

                    # Skip if already downloaded
                    if os.path.exists(filepath):
                        logger.info(f"Row {row_num}: Already exists - {filename}")
                        download_results["successful"].append(
                            {
                                "row": row_num,
                                "keyword": keyword,
                                "filename": filename,
                                "url": image_url,
                                "status": "already_exists",
                            }
                        )
                        continue

                    # Try to download
                    logger.info(
                        f"Row {row_num}: Downloading {keyword} from {image_url[:100]}..."
                    )

                    success = self._download_image_to_file(image_url, filepath, keyword)

                    if success:
                        download_results["successful"].append(
                            {
                                "row": row_num,
                                "keyword": keyword,
                                "filename": filename,
                                "url": image_url,
                                "status": "downloaded",
                            }
                        )
                        logger.info(
                            f"Row {row_num}: Successfully downloaded - {filename}"
                        )
                    else:
                        download_results["failed"].append(
                            {
                                "row": row_num,
                                "keyword": keyword,
                                "url": image_url,
                                "reason": "Download failed",
                            }
                        )
                        logger.error(f"Row {row_num}: Failed to download - {keyword}")

        except Exception as e:
            logger.error(f"Error during image download: {e}")
            raise

        # Print summary
        logger.info(f"\n=== IMAGE DOWNLOAD SUMMARY ===")
        logger.info(f"Successful downloads: {len(download_results['successful'])}")
        logger.info(f"Failed downloads: {len(download_results['failed'])}")
        logger.info(f"Skipped (no URL): {len(download_results['skipped'])}")

        return download_results

    def _download_image_to_file(self, url: str, filepath: str, keyword: str) -> bool:
        """Download an image from URL and save to file"""
        try:
            # Set up headers to mimic a browser request
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                "Accept": "image/webp,image/apng,image/*,*/*;q=0.8",
                "Accept-Language": "en-US,en;q=0.9",
                "Accept-Encoding": "gzip, deflate, br",
                "DNT": "1",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1",
            }

            # Try multiple download strategies
            strategies = [
                # Strategy 1: Direct download with headers
                lambda: requests.get(
                    url, headers=headers, timeout=30, allow_redirects=True
                ),
                # Strategy 2: Session-based download
                lambda: self._session_download(url, headers),
                # Strategy 3: Alternative image sources
                lambda: self._find_alternative_image(url),
            ]

            response = None
            for i, strategy in enumerate(strategies):
                try:
                    logger.debug(f"Trying download strategy {i+1} for {keyword}")
                    response = strategy()
                    if response and response.status_code == 200:
                        break
                except Exception as e:
                    logger.debug(f"Strategy {i+1} failed: {e}")
                    continue

            # Try placeholder if all strategies failed
            if not response or response.status_code != 200:
                placeholder_url = self._get_placeholder_image_for_concept(keyword)
                if placeholder_url:
                    logger.info(f"Trying placeholder image for {keyword}")
                    try:
                        response = requests.get(
                            placeholder_url, timeout=30, allow_redirects=True
                        )
                    except Exception as e:
                        logger.warning(f"Placeholder download failed: {e}")
                        return False

            if not response or response.status_code != 200:
                return False

            # Validate and process image
            try:
                image = Image.open(BytesIO(response.content))

                # Convert to RGB if necessary
                if image.mode != "RGB":
                    image = image.convert("RGB")

                # Resize if too large
                max_size = 2048
                if max(image.size) > max_size:
                    ratio = max_size / max(image.size)
                    new_size = tuple(int(dim * ratio) for dim in image.size)
                    image = image.resize(new_size, Image.Resampling.LANCZOS)

                # Save image
                image.save(filepath, format="JPEG", quality=85)
                logger.debug(f"Saved image: {filepath}")
                return True

            except Exception as e:
                logger.error(f"Error processing image for {keyword}: {e}")
                return False

        except Exception as e:
            logger.error(f"Error downloading image for {keyword}: {e}")
            return False

    def _download_images_with_range(
        self,
        output_dir: str,
        start_row: int = None,
        end_row: int = None,
        overwrite: bool = False,
    ) -> dict:
        """Download images with row range filtering"""
        import os
        from urllib.parse import urlparse

        # Create output directory
        os.makedirs(output_dir, exist_ok=True)

        download_results = {"successful": [], "failed": [], "skipped": []}

        logger.info(f"Starting image download to directory: {output_dir}")
        if start_row:
            logger.info(f"Starting from row: {start_row}")
        if end_row:
            logger.info(f"Ending at row: {end_row}")

        try:
            with open(self.csv_file, "r", encoding="utf-8") as f:
                reader = csv.DictReader(f)

                for row_num, row in enumerate(reader, start=2):
                    # Skip rows outside the specified range
                    if start_row is not None and row_num < start_row:
                        continue
                    if end_row is not None and row_num > end_row:
                        break

                    category = row.get("Main Category", "").strip()
                    keyword = row.get("Keyword/Concept", "").strip()
                    image_url = row.get("Image Source", "").strip()

                    if not image_url or not self._is_valid_image_url(image_url):
                        logger.info(f"Row {row_num}: Skipping - no valid image URL")
                        download_results["skipped"].append(
                            {
                                "row": row_num,
                                "keyword": keyword,
                                "reason": "No valid image URL",
                            }
                        )
                        continue

                    # Generate filename
                    safe_keyword = "".join(
                        c for c in keyword if c.isalnum() or c in (" ", "-", "_")
                    ).rstrip()
                    safe_keyword = safe_keyword.replace(" ", "_")

                    # Get file extension from URL
                    parsed_url = urlparse(image_url)
                    path = parsed_url.path.lower()
                    if path.endswith((".jpg", ".jpeg")):
                        ext = ".jpg"
                    elif path.endswith(".png"):
                        ext = ".png"
                    elif path.endswith(".webp"):
                        ext = ".webp"
                    else:
                        ext = ".jpg"  # Default

                    filename = f"row_{row_num:02d}_{safe_keyword}{ext}"
                    filepath = os.path.join(output_dir, filename)

                    # Skip if already downloaded (unless overwrite is True)
                    if os.path.exists(filepath) and not overwrite:
                        logger.info(f"Row {row_num}: Already exists - {filename}")
                        download_results["successful"].append(
                            {
                                "row": row_num,
                                "keyword": keyword,
                                "filename": filename,
                                "url": image_url,
                                "status": "already_exists",
                            }
                        )
                        continue

                    # Try to download
                    logger.info(
                        f"Row {row_num}: Downloading {keyword} from {image_url[:100]}..."
                    )

                    success = self._download_image_to_file(image_url, filepath, keyword)

                    if success:
                        download_results["successful"].append(
                            {
                                "row": row_num,
                                "keyword": keyword,
                                "filename": filename,
                                "url": image_url,
                                "status": (
                                    "downloaded"
                                    if overwrite or not os.path.exists(filepath)
                                    else "overwritten"
                                ),
                            }
                        )
                        logger.info(
                            f"Row {row_num}: Successfully downloaded - {filename}"
                        )
                    else:
                        download_results["failed"].append(
                            {
                                "row": row_num,
                                "keyword": keyword,
                                "url": image_url,
                                "reason": "Download failed",
                            }
                        )
                        logger.error(f"Row {row_num}: Failed to download - {keyword}")

        except Exception as e:
            logger.error(f"Error during image download: {e}")
            raise

        # Print summary
        logger.info(f"\n=== IMAGE DOWNLOAD SUMMARY ===")
        logger.info(f"Successful downloads: {len(download_results['successful'])}")
        logger.info(f"Failed downloads: {len(download_results['failed'])}")
        logger.info(f"Skipped (no URL): {len(download_results['skipped'])}")

        return download_results

    def _preview_image_downloads(
        self, output_dir: str, start_row: int = None, end_row: int = None
    ) -> dict:
        """Preview what images would be downloaded without actually downloading"""
        results = {"downloadable": 0, "skippable": 0, "total": 0}

        try:
            with open(self.csv_file, "r", encoding="utf-8") as f:
                reader = csv.DictReader(f)

                for row_num, row in enumerate(reader, start=2):
                    # Skip rows outside the specified range
                    if start_row is not None and row_num < start_row:
                        continue
                    if end_row is not None and row_num > end_row:
                        break

                    results["total"] += 1

                    keyword = row.get("Keyword/Concept", "").strip()
                    image_url = row.get("Image Source", "").strip()

                    if not image_url or not self._is_valid_image_url(image_url):
                        results["skippable"] += 1
                        print(f"Row {row_num}: SKIP - {keyword} (no valid URL)")
                    else:
                        results["downloadable"] += 1
                        print(
                            f"Row {row_num}: DOWNLOAD - {keyword} from {image_url[:50]}..."
                        )

        except Exception as e:
            logger.error(f"Error during preview: {e}")
            raise

        return results

    def _find_local_image(
        self, local_image_dir: str, row_num: int, keyword: str
    ) -> Optional[str]:
        """Find a local image file for the given row and keyword"""
        import os
        import glob

        if not local_image_dir or not os.path.exists(local_image_dir):
            return None

        # Try different filename patterns
        safe_keyword = "".join(
            c for c in keyword if c.isalnum() or c in (" ", "-", "_")
        ).rstrip()
        safe_keyword = safe_keyword.replace(" ", "_")

        patterns = [
            f"row_{row_num:02d}_{safe_keyword}.*",
            f"row_{row_num}_{safe_keyword}.*",
            f"{safe_keyword}.*",
            f"*{safe_keyword}*",
        ]

        for pattern in patterns:
            matches = glob.glob(os.path.join(local_image_dir, pattern))
            if matches:
                # Return the first match
                local_path = matches[0]
                logger.info(f"Found local image for {keyword}: {local_path}")
                return local_path

        logger.debug(f"No local image found for row {row_num}, keyword: {keyword}")
        return None

    def _encode_local_image(self, image_path: str) -> Optional[str]:
        """Encode a local image file as base64 for API"""
        try:
            with open(image_path, "rb") as f:
                image_data = f.read()

            # Open image with PIL to validate and potentially resize
            image = Image.open(BytesIO(image_data))

            # Convert to RGB if necessary
            if image.mode != "RGB":
                image = image.convert("RGB")

            # Resize if too large (OpenAI has size limits)
            max_size = 2048
            if max(image.size) > max_size:
                ratio = max_size / max(image.size)
                new_size = tuple(int(dim * ratio) for dim in image.size)
                image = image.resize(new_size, Image.Resampling.LANCZOS)
                logger.debug(f"Resized local image to {new_size}")

            # Convert back to bytes
            buffer = BytesIO()
            image.save(buffer, format="JPEG", quality=85)
            image_bytes = buffer.getvalue()

            # Encode as base64
            base64_image = base64.b64encode(image_bytes).decode("utf-8")
            logger.debug(
                f"Successfully encoded local image, size: {len(image_bytes)} bytes"
            )

            return f"data:image/jpeg;base64,{base64_image}"

        except Exception as e:
            logger.error(f"Failed to encode local image {image_path}: {e}")
            return None

    def _extract_json_from_text(self, text: str) -> Optional[dict]:
        """Try to extract JSON from a text response that might not be pure JSON"""
        import re

        try:
            # Look for JSON-like patterns in the text
            # Try to find content between { and }
            json_pattern = r"\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}"
            matches = re.findall(json_pattern, text, re.DOTALL)

            for match in matches:
                try:
                    parsed = json.loads(match)
                    # Check if it has the required fields
                    required_fields = [
                        "question",
                        "option_1",
                        "option_2",
                        "option_3",
                        "option_4",
                        "correct_option",
                    ]
                    if all(field in parsed for field in required_fields):
                        logger.info("Successfully extracted JSON from text response")
                        return parsed
                except json.JSONDecodeError:
                    continue

            # If no valid JSON found, try to parse the text manually
            logger.warning("No valid JSON found, attempting manual parsing")
            return self._manual_parse_response(text)

        except Exception as e:
            logger.error(f"Failed to extract JSON from text: {e}")
            return None

    def _manual_parse_response(self, text: str) -> Optional[dict]:
        """Manually parse response text to extract question and options"""
        try:
            logger.info(f"Attempting manual parsing of: {text}")
            lines = text.strip().split("\n")
            result = {}

            # Look for patterns like "Question:", "A)", "Option 1:", etc.
            for i, line in enumerate(lines):
                line = line.strip()
                if not line:
                    continue

                logger.debug(f"Processing line {i}: {line}")

                # Try to identify question - more flexible patterns
                if any(
                    keyword in line.lower()
                    for keyword in ["question:", "q:", "질문:", "문제:", "**question**"]
                ) or (
                    i == 0 and "?" in line
                ):  # First line with question mark
                    question_text = line
                    for prefix in ["question:", "q:", "질문:", "문제:", "**question**"]:
                        if prefix in question_text.lower():
                            question_text = question_text.split(":", 1)[-1].strip()
                            break
                    question_text = question_text.strip("*").strip()
                    if question_text:
                        result["question"] = question_text
                        logger.debug(f"Found question: {question_text}")

                # Try to identify options - more flexible patterns
                elif any(
                    pattern in line.lower()
                    for pattern in ["a)", "a.", "option a", "1)"]
                ):
                    option_text = self._extract_option_text(line)
                    if option_text:
                        result["option_1"] = option_text
                        logger.debug(f"Found option 1: {option_text}")

                elif any(
                    pattern in line.lower()
                    for pattern in ["b)", "b.", "option b", "2)"]
                ):
                    option_text = self._extract_option_text(line)
                    if option_text:
                        result["option_2"] = option_text
                        logger.debug(f"Found option 2: {option_text}")

                elif any(
                    pattern in line.lower()
                    for pattern in ["c)", "c.", "option c", "3)"]
                ):
                    option_text = self._extract_option_text(line)
                    if option_text:
                        result["option_3"] = option_text
                        logger.debug(f"Found option 3: {option_text}")

                elif any(
                    pattern in line.lower()
                    for pattern in ["d)", "d.", "option d", "4)"]
                ):
                    option_text = self._extract_option_text(line)
                    if option_text:
                        result["option_4"] = option_text
                        logger.debug(f"Found option 4: {option_text}")

                # Try to identify correct answer - more flexible patterns
                elif any(
                    keyword in line.lower()
                    for keyword in [
                        "correct:",
                        "answer:",
                        "정답:",
                        "correct answer:",
                        "**correct**",
                    ]
                ):
                    answer_text = line.split(":", 1)[-1].strip().upper()
                    # Extract just the letter
                    for char in answer_text:
                        if char in ["A", "B", "C", "D"]:
                            result["correct_option"] = char
                            logger.debug(f"Found correct answer: {char}")
                            break

            # Check if we have all required fields
            required_fields = [
                "question",
                "option_1",
                "option_2",
                "option_3",
                "option_4",
                "correct_option",
            ]

            found_fields = [
                field for field in required_fields if field in result and result[field]
            ]
            logger.info(
                f"Manual parsing found {len(found_fields)}/{len(required_fields)} fields: {found_fields}"
            )

            if all(field in result and result[field] for field in required_fields):
                logger.info("Successfully manually parsed response")
                return result
            else:
                logger.warning(f"Manual parsing incomplete. Found: {result}")
                return None

        except Exception as e:
            logger.error(f"Manual parsing failed: {e}")
            return None

    def _extract_option_text(self, line: str) -> str:
        """Extract option text from a line, removing prefixes"""
        # Remove common prefixes
        prefixes = [
            "a)",
            "b)",
            "c)",
            "d)",
            "a.",
            "b.",
            "c.",
            "d.",
            "1)",
            "2)",
            "3)",
            "4)",
            "option a",
            "option b",
            "option c",
            "option d",
        ]
        line_lower = line.lower()

        for prefix in prefixes:
            if line_lower.startswith(prefix):
                return line[len(prefix) :].strip()

        # If no prefix found, return the whole line
        return line.strip()

    def _generate_vqa_with_image(
        self,
        category: str,
        keyword: str,
        image_url: str,
        retry_count: int = 0,
        row_num: int = None,
        local_image_dir: str = None,
    ) -> Optional[VQAItem]:
        """Generate VQA content for items with both keyword and image"""
        try:
            encoded_image = None

            # First, try to use local image if available
            if local_image_dir and row_num:
                local_image_path = self._find_local_image(
                    local_image_dir, row_num, keyword
                )
                if local_image_path:
                    logger.info(f"Using local image for {keyword}: {local_image_path}")
                    encoded_image = self._encode_local_image(local_image_path)

            # If no local image, try downloading from URL
            if not encoded_image:
                encoded_image = self._download_and_encode_image(image_url)
                if not encoded_image:
                    # Try placeholder image for this keyword
                    placeholder_url = self._get_placeholder_image_for_concept(keyword)
                    if placeholder_url:
                        logger.info(
                            f"Trying placeholder image for {keyword}: {placeholder_url}"
                        )
                        encoded_image = self._download_and_encode_image(placeholder_url)

            if not encoded_image:
                logger.error(f"Failed to process any image for {keyword}")
                return None
            system_prompt = f"""You are an expert in Korean culture creating Visual Question Answering content.

CRITICAL REQUIREMENTS:
1. Question must be IMPOSSIBLE to answer without seeing the image
2. NEVER mention "{keyword}" or any location names in the question
3. Use only generic visual descriptions: "this structure", "this building", "the architectural elements shown"
4. Question must require BOTH visual analysis AND deep Korean cultural knowledge
5. All content must be in ENGLISH only
6. Create expert-level difficulty requiring multi-hop reasoning
7. Focus on architectural styles, construction techniques, cultural significance visible in the image

Category context: {category}
Cultural concept: {keyword}

Generate a question about Korean cultural knowledge that can ONLY be answered by analyzing the visual elements in the image.

Example approach: "The architectural style visible in this structure demonstrates which traditional Korean construction principle?"

Respond with ONLY this JSON format:
{{
    "question": "Your question here (generic visual terms only, no location names)",
    "option_1": "Option A",
    "option_2": "Option B",
    "option_3": "Option C",
    "option_4": "Option D",
    "correct_option": "A"
}}"""

            user_prompt = f"""Category: {category}
Keyword/Concept: {keyword}

Please analyze the image and generate an expert-level VQA question following all the rules provided."""

            response = self.client.chat.completions.create(
                model="o4-mini-2025-04-16",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": user_prompt},
                            {"type": "image_url", "image_url": {"url": encoded_image}},
                        ],
                    },
                ],
                max_completion_tokens=1000,
            )

            content = response.choices[0].message.content
            logger.info(f"Raw API response type: {type(content)}")
            logger.info(f"Raw API response is None: {content is None}")

            if content is None:
                logger.error("API returned None content")
                return None

            content = content.strip()
            logger.info(f"Content length after strip: {len(content)}")
            logger.info(f"Raw API response: {repr(content[:200])}...")
            logger.info(
                f"FULL API response: {repr(content)}"
            )  # Log the complete response with repr

            # Parse JSON response
            if content.startswith("```json"):
                content = content[7:-3].strip()
            elif content.startswith("```"):
                content = content[3:-3].strip()

            # Additional cleaning for common issues
            if content.startswith("```") and content.endswith("```"):
                content = content[3:-3].strip()

            logger.info(f"Cleaned content for JSON parsing: {repr(content[:200])}...")

            # Check if content is empty
            if not content:
                logger.error(
                    "Content is empty after cleaning - possibly content filtered"
                )
                if retry_count < 1:
                    logger.info(
                        f"Retrying image generation (attempt {retry_count + 1})"
                    )
                    time.sleep(2)  # Brief delay before retry
                    return self._generate_vqa_with_image(
                        category,
                        keyword,
                        image_url,
                        retry_count + 1,
                        row_num,
                        local_image_dir,
                    )
                else:
                    logger.info("Falling back to text-only generation for this item")
                    # Fall back to text-only generation
                    return self._generate_vqa_without_image(category, keyword)

            # Try to parse JSON with better error handling
            try:
                vqa_data = json.loads(content)
            except json.JSONDecodeError as e:
                logger.error(f"JSON parsing failed: {e}")
                logger.error(f"Full content that failed to parse: {repr(content)}")

                # Try to extract JSON from text response
                vqa_data = self._extract_json_from_text(content)
                if not vqa_data:
                    logger.error("Could not extract valid JSON from response")
                    return None

            return VQAItem(
                main_category=category,
                question=vqa_data["question"],
                option_1=vqa_data["option_1"],
                option_2=vqa_data["option_2"],
                option_3=vqa_data["option_3"],
                option_4=vqa_data["option_4"],
                correct_option=vqa_data["correct_option"],
                image_source=image_url,
                keyword_concept=keyword,
            )

        except Exception as e:
            logger.error(
                f"Error generating VQA with image for {category}/{keyword}: {e}"
            )
            return None

    def _generate_vqa_without_image(
        self, category: str, keyword: str
    ) -> Optional[VQAItem]:
        """Generate VQA content for items with keyword but no image"""
        try:
            system_prompt = f"""Create a Korean cultural knowledge question.

Requirements:
- English only
- Expert-level difficulty
- About {keyword} in {category} category
- Do not mention "{keyword}" directly in question
- Focus on cultural significance, historical context, or architectural principles

JSON format only:
{{
    "question": "Your question here",
    "option_1": "Option A",
    "option_2": "Option B",
    "option_3": "Option C",
    "option_4": "Option D",
    "correct_option": "A"
}}"""

            user_prompt = f"""Category: {category}
Keyword/Concept: {keyword}

Please generate an expert-level VQA question that tests deep Korean cultural knowledge related to this concept, without requiring visual analysis."""

            response = self.client.chat.completions.create(
                model="o4-mini-2025-04-16",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ],
                max_completion_tokens=1000,
            )

            content = response.choices[0].message.content
            logger.info(f"Raw API response type (text-only): {type(content)}")
            logger.info(f"Raw API response is None (text-only): {content is None}")

            if content is None:
                logger.error("API returned None content (text-only)")
                return None

            content = content.strip()
            logger.info(f"Content length after strip (text-only): {len(content)}")
            logger.info(f"Raw API response (text-only): {repr(content[:200])}...")
            logger.info(
                f"FULL API response (text-only): {repr(content)}"
            )  # Log the complete response with repr

            # Parse JSON response
            if content.startswith("```json"):
                content = content[7:-3].strip()
            elif content.startswith("```"):
                content = content[3:-3].strip()

            # Additional cleaning for common issues
            if content.startswith("```") and content.endswith("```"):
                content = content[3:-3].strip()

            logger.info(
                f"Cleaned content for JSON parsing (text-only): {repr(content[:200])}..."
            )

            # Check if content is empty
            if not content:
                logger.error("Content is empty after cleaning (text-only)")
                return None

            # Try to parse JSON with better error handling
            try:
                vqa_data = json.loads(content)
            except json.JSONDecodeError as e:
                logger.error(f"JSON parsing failed (text-only): {e}")
                logger.error(f"Full content that failed to parse: {repr(content)}")

                # Try to extract JSON from text response
                vqa_data = self._extract_json_from_text(content)
                if not vqa_data:
                    logger.error(
                        "Could not extract valid JSON from response (text-only)"
                    )
                    return None

            return VQAItem(
                main_category=category,
                question=vqa_data["question"],
                option_1=vqa_data["option_1"],
                option_2=vqa_data["option_2"],
                option_3=vqa_data["option_3"],
                option_4=vqa_data["option_4"],
                correct_option=vqa_data["correct_option"],
                image_source="",
                keyword_concept=keyword,
            )

        except Exception as e:
            logger.error(
                f"Error generating VQA without image for {category}/{keyword}: {e}"
            )
            return None

    def _force_generate_vqa(self, category: str, keyword: str) -> VQAItem:
        """Force generate a VQA item using a simplified approach"""
        try:
            logger.info(f"Force generating VQA for {category}/{keyword}")

            # Create a very simple, generic question
            simple_prompt = f"""Create a Korean cultural knowledge question about {keyword} in the {category} category.

Requirements:
- Question must be in English only
- Expert-level difficulty
- 4 multiple choice options
- Do not mention "{keyword}" directly in the question

JSON format:
{{
    "question": "Question about Korean cultural knowledge",
    "option_1": "Option A",
    "option_2": "Option B",
    "option_3": "Option C",
    "option_4": "Option D",
    "correct_option": "A"
}}"""

            response = self.client.chat.completions.create(
                model="o4-mini-2025-04-16",
                messages=[{"role": "user", "content": simple_prompt}],
                max_completion_tokens=500,
            )

            content = response.choices[0].message.content.strip()
            logger.info(f"Force generation response: {content[:100]}...")

            # Try to parse JSON
            if content.startswith("```json"):
                content = content[7:-3].strip()
            elif content.startswith("```"):
                content = content[3:-3].strip()

            try:
                vqa_data = json.loads(content)
            except json.JSONDecodeError:
                # If JSON parsing fails, try to get a keyword-specific question
                logger.warning(
                    "Force generation JSON parsing failed, checking for keyword-specific question"
                )
                vqa_data = self._create_keyword_specific_question(category, keyword)
                if not vqa_data:
                    logger.warning(
                        f"No predefined question for {keyword}, skipping entry"
                    )
                    return None

            return VQAItem(
                main_category=category,
                question=vqa_data["question"],
                option_1=vqa_data["option_1"],
                option_2=vqa_data["option_2"],
                option_3=vqa_data["option_3"],
                option_4=vqa_data["option_4"],
                correct_option=vqa_data["correct_option"],
                image_source="",
                keyword_concept=keyword,
            )

        except Exception as e:
            logger.error(f"Force generation failed for {category}/{keyword}: {e}")
            # Try to get a keyword-specific question as last resort
            vqa_data = self._create_keyword_specific_question(category, keyword)
            if not vqa_data:
                logger.warning(f"No predefined question for {keyword}, skipping entry")
                return None

            return VQAItem(
                main_category=category,
                question=vqa_data["question"],
                option_1=vqa_data["option_1"],
                option_2=vqa_data["option_2"],
                option_3=vqa_data["option_3"],
                option_4=vqa_data["option_4"],
                correct_option=vqa_data["correct_option"],
                image_source="",
                keyword_concept=keyword,
            )

    def _create_keyword_specific_question(self, category: str, keyword: str) -> dict:
        """Create a keyword-specific question when AI generation fails"""

        # Define keyword-specific questions
        keyword_questions = {
            # Architecture
            "고려대학교": {
                "question": "Which architectural style was predominantly used in early 20th century Korean university buildings to symbolize modern education while maintaining cultural identity?",
                "option_1": "Neo-Gothic Revival with Korean decorative elements",
                "option_2": "Pure Western Classical architecture",
                "option_3": "Traditional hanok wooden structures",
                "option_4": "Japanese colonial administrative style",
                "correct_option": "A",
            },
            "제주 돌집": {
                "question": "What is the primary architectural purpose of the distinctive stone construction method used in traditional Jeju island dwellings?",
                "option_1": "Protection against strong coastal winds and volcanic terrain",
                "option_2": "Aesthetic appeal for tourism purposes",
                "option_3": "Religious significance in shamanic practices",
                "option_4": "Economic efficiency in construction costs",
                "correct_option": "A",
            },
            "운현궁": {
                "question": "Which architectural feature distinguishes royal residential palaces from common aristocratic homes in late Joseon dynasty Korea?",
                "option_1": "Elevated foundation platforms and multiple courtyard systems",
                "option_2": "Use of bright colors on exterior walls",
                "option_3": "Western-style furniture and decorations",
                "option_4": "Single-story construction with large windows",
                "correct_option": "A",
            },
            "남산타워": {
                "question": "What cultural significance does the placement of communication towers on sacred mountains hold in Korean urban planning?",
                "option_1": "Balancing modern technology with traditional feng shui principles",
                "option_2": "Purely functional placement for optimal signal coverage",
                "option_3": "Rejection of traditional spatial concepts",
                "option_4": "Imitation of Western urban development models",
                "correct_option": "A",
            },
            "월정교": {
                "question": "Which construction principle was most important in traditional Korean bridge architecture for spanning water bodies?",
                "option_1": "Harmony between structural engineering and natural landscape",
                "option_2": "Maximum load capacity regardless of aesthetics",
                "option_3": "Imitation of Chinese architectural models",
                "option_4": "Use of imported materials for durability",
                "correct_option": "A",
            },
            "신라대종": {
                "question": "What distinguishes Korean bell-casting techniques from those of neighboring East Asian countries?",
                "option_1": "Unique sound resonance achieved through specific bronze alloy compositions",
                "option_2": "Larger size compared to Chinese and Japanese bells",
                "option_3": "Exclusive use of Buddhist religious motifs",
                "option_4": "Simpler casting methods requiring less technical skill",
                "correct_option": "A",
            },
            "한강다리": {
                "question": "Which engineering principle guided the construction of major river bridges during Korea's rapid modernization period?",
                "option_1": "Integration of modern engineering with consideration for traditional river management",
                "option_2": "Pure adoption of Western bridge construction methods",
                "option_3": "Minimal environmental impact assessment",
                "option_4": "Focus solely on economic efficiency",
                "correct_option": "A",
            },
            "명동": {
                "question": "What urban planning principle shaped the development of major commercial districts in post-war Seoul?",
                "option_1": "Dense mixed-use development accommodating both local and international commerce",
                "option_2": "Strict separation of commercial and residential areas",
                "option_3": "Preservation of traditional market structures",
                "option_4": "Imitation of American suburban shopping mall concepts",
                "correct_option": "A",
            },
            # Food & Culture
            "떡집": {
                "question": "Which traditional Korean architectural element was essential for rice cake shop design to ensure proper food preservation?",
                "option_1": "Elevated wooden floors with natural ventilation systems",
                "option_2": "Underground storage chambers",
                "option_3": "Western-style refrigeration units",
                "option_4": "Sealed concrete storage areas",
                "correct_option": "A",
            },
            "탑골공원": {
                "question": "What role did traditional Korean pagoda architecture play in urban park design during the Japanese colonial period?",
                "option_1": "Preservation of cultural identity within modernizing urban spaces",
                "option_2": "Pure decorative function without cultural significance",
                "option_3": "Adaptation to Japanese architectural preferences",
                "option_4": "Rejection of traditional Korean design principles",
                "correct_option": "A",
            },
            "광화문": {
                "question": "Which principle governed the architectural design of main palace gates in Joseon dynasty Korea?",
                "option_1": "Symbolic representation of royal authority through proportional harmony",
                "option_2": "Maximum defensive capability against military attacks",
                "option_3": "Imitation of Chinese imperial gate designs",
                "option_4": "Economic efficiency in construction materials",
                "correct_option": "A",
            },
            # Modern Korean Branding & Culture
            "별마당 도서관": {
                "question": "What design philosophy underlies the creation of large-scale cultural spaces within Korean commercial complexes?",
                "option_1": "Integration of public cultural access with private commercial development",
                "option_2": "Separation of cultural and commercial functions",
                "option_3": "Prioritization of retail space over cultural amenities",
                "option_4": "Imitation of Western shopping mall concepts",
                "correct_option": "A",
            },
            "코엑스": {
                "question": "Which urban planning principle guided the development of Korea's major convention and exhibition centers?",
                "option_1": "Multi-functional spaces combining business, culture, and retail",
                "option_2": "Single-purpose convention facilities",
                "option_3": "Residential-focused mixed-use development",
                "option_4": "Industrial-style exhibition halls",
                "correct_option": "A",
            },
            "티니핑": {
                "question": "What cultural strategy do Korean character brands employ to achieve both domestic success and international appeal?",
                "option_1": "Combining universal emotional themes with distinctly Korean aesthetic elements",
                "option_2": "Complete adoption of Western character design principles",
                "option_3": "Focus exclusively on domestic market preferences",
                "option_4": "Rejection of traditional Korean cultural references",
                "correct_option": "A",
            },
            "삼성": {
                "question": "Which business philosophy has been central to Korean chaebol expansion strategies since the 1960s?",
                "option_1": "Diversified conglomerate structure with vertical integration",
                "option_2": "Specialized single-industry focus",
                "option_3": "Decentralized small business networks",
                "option_4": "Foreign partnership-dependent growth",
                "correct_option": "A",
            },
            "네이버": {
                "question": "What distinguishes Korean internet portal services from their global counterparts?",
                "option_1": "Comprehensive ecosystem integration of multiple digital services",
                "option_2": "Focus on single-function search capabilities",
                "option_3": "Reliance on foreign technology platforms",
                "option_4": "Limited mobile application development",
                "correct_option": "A",
            },
            "다이소": {
                "question": "Which retail strategy enabled Korean discount chains to expand successfully across Asia?",
                "option_1": "Standardized product curation adapted to local market preferences",
                "option_2": "High-end luxury positioning",
                "option_3": "Traditional department store formats",
                "option_4": "Online-only sales channels",
                "correct_option": "A",
            },
            "라인프렌즈": {
                "question": "How do Korean character merchandising brands leverage digital platforms for global expansion?",
                "option_1": "Cross-platform character development with integrated digital and physical products",
                "option_2": "Traditional licensing-only business models",
                "option_3": "Focus on single-platform character development",
                "option_4": "Rejection of digital marketing strategies",
                "correct_option": "A",
            },
            "올리브영": {
                "question": "What retail innovation strategy has driven the success of Korean beauty and health store chains?",
                "option_1": "Curated K-beauty product selection with experiential retail environments",
                "option_2": "Traditional pharmacy-style product displays",
                "option_3": "Focus on international beauty brands only",
                "option_4": "Online-exclusive sales models",
                "correct_option": "A",
            },
            # Traditional Korean Architecture
            "종묘": {
                "question": "What architectural principle governs the spatial arrangement of royal ancestral shrine complexes in Korea?",
                "option_1": "Hierarchical layout reflecting Confucian concepts of ancestral veneration",
                "option_2": "Circular arrangements based on Buddhist mandala designs",
                "option_3": "Linear progression following Western architectural principles",
                "option_4": "Random placement emphasizing natural landscape integration",
                "correct_option": "A",
            },
            "독립문": {
                "question": "What symbolic architectural element distinguishes Korean independence monuments from colonial-era structures?",
                "option_1": "Traditional Korean roof forms combined with Western triumphal arch concepts",
                "option_2": "Pure Western neoclassical design without Korean elements",
                "option_3": "Traditional hanok wooden construction methods",
                "option_4": "Japanese colonial architectural styling",
                "correct_option": "A",
            },
            "남대문": {
                "question": "Which defensive architectural feature characterizes the design of Seoul's historic city gates?",
                "option_1": "Multi-tiered roof structures with integrated defensive positioning",
                "option_2": "Single-story construction for easy maintenance",
                "option_3": "Western-style fortification walls",
                "option_4": "Japanese castle gate design principles",
                "correct_option": "A",
            },
            "덕수궁": {
                "question": "What architectural adaptation occurred in Korean royal palaces during the late Joseon period?",
                "option_1": "Integration of Western architectural elements while maintaining Korean spatial principles",
                "option_2": "Complete replacement of traditional Korean architecture",
                "option_3": "Strict preservation of medieval Korean building methods",
                "option_4": "Adoption of Japanese palace design standards",
                "correct_option": "A",
            },
            "동대문": {
                "question": "How does the architectural design of Seoul's Great East Gate reflect Korean urban planning principles?",
                "option_1": "Strategic positioning integrating defensive needs with commercial accessibility",
                "option_2": "Purely decorative function without practical considerations",
                "option_3": "Western-style urban gate design",
                "option_4": "Japanese colonial urban planning concepts",
                "correct_option": "A",
            },
            # Korean Food Culture
            "찌개집": {
                "question": "What cultural dining principle underlies the design and operation of traditional Korean stew restaurants?",
                "option_1": "Communal sharing culture reflected in large pot cooking and table arrangements",
                "option_2": "Individual portion service following Western dining customs",
                "option_3": "Fast-food efficiency prioritizing speed over social interaction",
                "option_4": "Formal fine-dining presentation emphasizing visual aesthetics",
                "correct_option": "A",
            },
            "재래시장": {
                "question": "Which social function has traditional Korean markets maintained despite modern retail competition?",
                "option_1": "Community gathering spaces preserving local food culture and social relationships",
                "option_2": "Purely commercial transactions without social interaction",
                "option_3": "Tourist attractions disconnected from local community needs",
                "option_4": "Wholesale distribution centers for large retailers",
                "correct_option": "A",
            },
            "BBQ": {
                "question": "What innovation strategy enabled Korean fried chicken chains to differentiate from American fast-food competitors?",
                "option_1": "Double-frying technique with Korean sauce flavors and premium ingredients",
                "option_2": "Direct copying of American fried chicken recipes",
                "option_3": "Focus on lowest-cost production methods",
                "option_4": "Elimination of Korean flavor preferences",
                "correct_option": "A",
            },
            "교촌": {
                "question": "How do Korean premium chicken restaurant chains position themselves differently from fast-food competitors?",
                "option_1": "Emphasis on traditional Korean cooking methods with modern quality standards",
                "option_2": "Adoption of Western fast-food operational models",
                "option_3": "Focus on speed and convenience over food quality",
                "option_4": "Elimination of Korean culinary traditions",
                "correct_option": "A",
            },
            "맘스터치": {
                "question": "What localization strategy do Korean burger chains employ to compete with global fast-food brands?",
                "option_1": "Korean-style burger ingredients and flavors adapted to local taste preferences",
                "option_2": "Exact replication of American burger chain menus",
                "option_3": "Focus on traditional Korean dishes only",
                "option_4": "Rejection of burger format in favor of Korean alternatives",
                "correct_option": "A",
            },
            # Korean Fashion and Lifestyle
            "한국교복": {
                "question": "What design philosophy has guided the evolution of Korean school uniforms since the 1980s?",
                "option_1": "Balance between formal educational dignity and contemporary youth fashion trends",
                "option_2": "Strict adherence to traditional Korean clothing styles",
                "option_3": "Complete adoption of Western school uniform designs",
                "option_4": "Elimination of any fashion considerations for purely functional design",
                "correct_option": "A",
            },
            "퓨전한복": {
                "question": "How does contemporary fusion hanbok design address modern Korean cultural identity?",
                "option_1": "Adaptation of traditional silhouettes for contemporary lifestyle while preserving cultural essence",
                "option_2": "Complete abandonment of traditional Korean clothing elements",
                "option_3": "Strict replication of historical hanbok without modern modifications",
                "option_4": "Replacement of Korean elements with Western fashion trends",
                "correct_option": "A",
            },
            "생활한복": {
                "question": "What cultural need does everyday hanbok design address in modern Korean society?",
                "option_1": "Integration of traditional Korean aesthetics into practical daily wear",
                "option_2": "Formal ceremonial use only without daily life applications",
                "option_3": "Tourist costume purposes disconnected from Korean cultural practice",
                "option_4": "Historical reenactment without contemporary relevance",
                "correct_option": "A",
            },
            "아이돌패션": {
                "question": "How has K-pop idol fashion influenced global fashion trends and Korean cultural soft power?",
                "option_1": "Creative fusion of Korean aesthetic sensibilities with international fashion innovation",
                "option_2": "Direct copying of Western pop star fashion without Korean elements",
                "option_3": "Focus exclusively on traditional Korean clothing styles",
                "option_4": "Rejection of fashion as a cultural expression medium",
                "correct_option": "A",
            },
            # Korean Lifestyle Districts
            "홍대": {
                "question": "What urban development principle has shaped Hongdae's emergence as a youth culture district?",
                "option_1": "Organic growth around university culture fostering creative industries and nightlife",
                "option_2": "Top-down government planning for commercial development",
                "option_3": "Replication of Western entertainment district models",
                "option_4": "Preservation of traditional Korean neighborhood characteristics",
                "correct_option": "A",
            },
            "가로수길": {
                "question": "How does Garosu-gil represent the evolution of Korean retail and lifestyle culture?",
                "option_1": "Boutique shopping culture emphasizing individual style and artisanal products",
                "option_2": "Large-scale department store retail formats",
                "option_3": "Traditional Korean market structures",
                "option_4": "American-style shopping mall development",
                "correct_option": "A",
            },
            "설빙": {
                "question": "What innovation strategy enabled Korean dessert chains to create new market categories?",
                "option_1": "Fusion of traditional Korean ingredients with modern dessert presentation techniques",
                "option_2": "Direct copying of Western dessert shop concepts",
                "option_3": "Focus on traditional Korean sweets without modern adaptations",
                "option_4": "Elimination of Korean flavor profiles in favor of international tastes",
                "correct_option": "A",
            },
            "쿠팡로켓배송": {
                "question": "What logistics innovation strategy has enabled Korean e-commerce platforms to compete with global giants?",
                "option_1": "Ultra-fast delivery systems adapted to Korean urban density and consumer expectations",
                "option_2": "Standard international shipping timeframes",
                "option_3": "Focus on rural delivery over urban markets",
                "option_4": "Elimination of same-day delivery services",
                "correct_option": "A",
            },
        }

        # Return keyword-specific question if available, otherwise SKIP to avoid duplicates
        if keyword in keyword_questions:
            return keyword_questions[keyword]
        else:
            # SKIP entries without predefined questions to avoid duplicate generic questions
            logger.warning(
                f"No predefined question for keyword '{keyword}', SKIPPING to avoid duplicate generic questions"
            )
            return None

    def process_csv(
        self,
        save_progress: bool = True,
        start_row: int = None,
        end_row: int = None,
        local_image_dir: str = None,
    ) -> List[VQAItem]:
        """Process the CSV file and generate VQA content"""
        results = []

        # Create progress output file
        if save_progress:
            base_name = os.path.splitext(os.path.basename(self.csv_file))[0]
            self.progress_file = f"{base_name}_vqa_progress.csv"
            logger.info(f"Progress will be saved to: {self.progress_file}")

        try:
            with open(self.csv_file, "r", encoding="utf-8") as f:
                reader = csv.DictReader(f)

                for row_num, row in enumerate(
                    reader, start=2
                ):  # Start at 2 because of header
                    # Skip rows outside the specified range
                    if start_row is not None and row_num < start_row:
                        continue
                    if end_row is not None and row_num > end_row:
                        break

                    category = row.get("Main Category", "").strip()
                    keyword = row.get("Keyword/Concept", "").strip()
                    image_url = row.get("Image Source", "").strip()

                    # Skip completely empty rows (preserve them as empty in output)
                    if not category and not keyword and not image_url:
                        logger.info(f"Row {row_num}: Skipping empty row")
                        results.append(
                            VQAItem(
                                main_category="",
                                question="",
                                option_1="",
                                option_2="",
                                option_3="",
                                option_4="",
                                correct_option="",
                                image_source="",
                                keyword_concept="",
                            )
                        )
                        continue

                    # Skip rows without category or keyword
                    if not category or not keyword:
                        logger.warning(
                            f"Row {row_num}: Missing category or keyword, skipping"
                        )
                        results.append(
                            VQAItem(
                                main_category=category,
                                question="",
                                option_1="",
                                option_2="",
                                option_3="",
                                option_4="",
                                correct_option="",
                                image_source=image_url,
                                keyword_concept=keyword,
                            )
                        )
                        continue

                    logger.info(f"Row {row_num}: Processing {category}/{keyword}")

                    # Determine processing type based on image availability
                    vqa_item = None

                    # Try image-based generation first if URL exists
                    if image_url and self._is_valid_image_url(image_url):
                        logger.info(f"Row {row_num}: Attempting VQA with image")
                        vqa_item = self._generate_vqa_with_image(
                            category, keyword, image_url, 0, row_num, local_image_dir
                        )

                    # If image-based failed or no image, try text-only generation
                    if not vqa_item and keyword:
                        logger.info(
                            f"Row {row_num}: Attempting VQA without image (fallback)"
                        )
                        vqa_item = self._generate_vqa_without_image(category, keyword)

                    # If still no result, force generation with generic approach
                    if not vqa_item and keyword:
                        logger.warning(f"Row {row_num}: Forcing generic VQA generation")
                        vqa_item = self._force_generate_vqa(category, keyword)

                    if vqa_item:
                        results.append(vqa_item)
                        logger.info(f"Row {row_num}: Successfully generated VQA")
                    else:
                        # Add empty row to maintain structure
                        results.append(
                            VQAItem(
                                main_category=category,
                                question="",
                                option_1="",
                                option_2="",
                                option_3="",
                                option_4="",
                                correct_option="",
                                image_source=image_url,
                                keyword_concept=keyword,
                            )
                        )
                        logger.error(f"Row {row_num}: Failed to generate VQA")

                    # Save progress after each row
                    if save_progress:
                        self._save_progress(results)
                        logger.info(f"Progress saved: {len(results)} rows completed")

                    # Add delay to respect API rate limits
                    time.sleep(1)

        except FileNotFoundError:
            logger.error(f"CSV file not found: {self.csv_file}")
            raise
        except Exception as e:
            logger.error(f"Error processing CSV file: {e}")
            raise

        return results

    def _save_progress(self, results: List[VQAItem]):
        """Save current progress to CSV file"""
        try:
            with open(self.progress_file, "w", newline="", encoding="utf-8") as f:
                fieldnames = [
                    "Main Category",
                    "Question",
                    "Option 1",
                    "Option 2",
                    "Option 3",
                    "Option 4",
                    "Correct Option",
                    "Image Source",
                    "Keyword/Concept",
                ]
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()

                for item in results:
                    writer.writerow(
                        {
                            "Main Category": item.main_category,
                            "Question": item.question,
                            "Option 1": item.option_1,
                            "Option 2": item.option_2,
                            "Option 3": item.option_3,
                            "Option 4": item.option_4,
                            "Correct Option": item.correct_option,
                            "Image Source": item.image_source,
                            "Keyword/Concept": item.keyword_concept,
                        }
                    )
        except Exception as e:
            logger.error(f"Failed to save progress: {e}")

    def save_results(
        self,
        results: List[VQAItem],
        output_format: str = "csv",
        output_file: str = None,
    ):
        """Save VQA results to file"""
        if not output_file:
            base_name = os.path.splitext(os.path.basename(self.csv_file))[0]
            output_file = f"{base_name}_vqa_generated.{output_format}"

        try:
            if output_format.lower() == "csv":
                self._save_to_csv(results, output_file)
            elif output_format.lower() == "json":
                self._save_to_json(results, output_file)
            else:
                raise ValueError(f"Unsupported output format: {output_format}")

            logger.info(f"Results saved to {output_file}")

        except Exception as e:
            logger.error(f"Error saving results: {e}")
            raise

    def _save_to_csv(self, results: List[VQAItem], output_file: str):
        """Save results to CSV format"""
        with open(output_file, "w", newline="", encoding="utf-8") as f:
            fieldnames = [
                "Main Category",
                "Question",
                "Option 1",
                "Option 2",
                "Option 3",
                "Option 4",
                "Correct Option",
                "Image Source",
                "Keyword/Concept",
            ]
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()

            for item in results:
                writer.writerow(
                    {
                        "Main Category": item.main_category,
                        "Question": item.question,
                        "Option 1": item.option_1,
                        "Option 2": item.option_2,
                        "Option 3": item.option_3,
                        "Option 4": item.option_4,
                        "Correct Option": item.correct_option,
                        "Image Source": item.image_source,
                        "Keyword/Concept": item.keyword_concept,
                    }
                )

    def _save_to_json(self, results: List[VQAItem], output_file: str):
        """Save results to JSON format"""
        json_data = []
        for item in results:
            json_data.append(
                {
                    "main_category": item.main_category,
                    "question": item.question,
                    "options": {
                        "A": item.option_1,
                        "B": item.option_2,
                        "C": item.option_3,
                        "D": item.option_4,
                    },
                    "correct_option": item.correct_option,
                    "image_source": item.image_source,
                    "keyword_concept": item.keyword_concept,
                }
            )

        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2)


def main():
    """Main function to run the VQA generator"""
    parser = argparse.ArgumentParser(
        description="Generate VQA content using OpenAI o4-mini-2025-04-16",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python vqa_generator.py --api-key YOUR_API_KEY
  python vqa_generator.py --api-key YOUR_API_KEY --output-format json
  python vqa_generator.py --api-key YOUR_API_KEY --output-file my_vqa.csv
  python vqa_generator.py --api-key YOUR_API_KEY --rules custom_rules.md --csv custom_data.csv
        """,
    )

    parser.add_argument(
        "--api-key",
        help="OpenAI API key (or set OPENAI_API_KEY environment variable). Not required for --download-images mode.",
    )

    parser.add_argument(
        "--rules",
        default="/mnt/raid6/junkim100/east-asia/VQA_Generation_Rules.md",
        help="Path to VQA generation rules markdown file",
    )

    parser.add_argument(
        "--csv",
        default="/mnt/raid6/junkim100/east-asia/VQA .csv",
        help="Path to input CSV file",
    )

    parser.add_argument(
        "--output-format",
        choices=["csv", "json"],
        default="csv",
        help="Output format (csv or json)",
    )

    parser.add_argument(
        "--output-file", help="Output file path (auto-generated if not specified)"
    )

    parser.add_argument(
        "--dry-run", action="store_true", help="Process only first 5 rows for testing"
    )

    parser.add_argument(
        "--no-progress",
        action="store_true",
        help="Disable progress saving after each row",
    )

    parser.add_argument(
        "--start-row", type=int, help="Start processing from this row number"
    )

    parser.add_argument("--end-row", type=int, help="End processing at this row number")

    parser.add_argument(
        "--download-images",
        action="store_true",
        help="Download all images from CSV to local directory",
    )

    parser.add_argument(
        "--image-dir",
        default="downloaded_images",
        help="Directory to save downloaded images (default: downloaded_images)",
    )

    parser.add_argument(
        "--use-local-images",
        help="Directory containing local images to use instead of downloading (e.g., my_images/)",
    )

    args = parser.parse_args()

    # Get API key from argument or environment variable (not required for image download)
    api_key = args.api_key or os.getenv("OPENAI_API_KEY")
    if not api_key and not args.download_images:
        logger.error(
            "OpenAI API key is required. Use --api-key or set OPENAI_API_KEY environment variable."
        )
        return 1

    try:
        # Initialize generator
        logger.info("Initializing VQA Generator...")
        # Use dummy API key for image download mode
        init_api_key = api_key if api_key else "dummy-key-for-image-download"
        generator = VQAGenerator(init_api_key, args.rules, args.csv)

        # Handle image download mode
        if args.download_images:
            logger.info("Starting image download mode...")
            download_results = generator.download_images_from_csv(args.image_dir)

            # Save download report
            report_file = f"{args.image_dir}_download_report.json"
            import json

            with open(report_file, "w", encoding="utf-8") as f:
                json.dump(download_results, f, ensure_ascii=False, indent=2)

            logger.info(f"Download report saved to: {report_file}")
            return 0

        # Process CSV
        logger.info("Processing CSV file...")
        save_progress = not args.no_progress
        results = generator.process_csv(
            save_progress=save_progress,
            start_row=args.start_row,
            end_row=args.end_row,
            local_image_dir=args.use_local_images,
        )

        # Limit results for dry run
        if args.dry_run:
            results = results[:5]
            logger.info("Dry run mode: Processing only first 5 rows")

        # Save results
        logger.info(f"Saving {len(results)} results...")
        generator.save_results(results, args.output_format, args.output_file)

        # Print summary
        successful_generations = sum(1 for r in results if r.question.strip())
        logger.info(f"Generation complete!")
        logger.info(f"Total rows processed: {len(results)}")
        logger.info(f"Successful generations: {successful_generations}")
        logger.info(f"Failed generations: {len(results) - successful_generations}")

        return 0

    except KeyboardInterrupt:
        logger.info("Generation interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"Error during generation: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
