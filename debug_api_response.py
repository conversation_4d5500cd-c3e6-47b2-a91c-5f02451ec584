#!/usr/bin/env python3
"""
Debug script to test API response format from o4-mini-2025-04-16
"""

import os
import json
from openai import OpenAI

def test_api_response():
    """Test what the API actually returns"""
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("Please set OPENAI_API_KEY environment variable")
        return
    
    client = OpenAI(api_key=api_key)
    
    # Simple test prompt
    system_prompt = """You are an expert in Korean culture. Generate a VQA question.

Return your response in this exact JSON format:
{
    "question": "Your question here",
    "option_1": "Option A text",
    "option_2": "Option B text", 
    "option_3": "Option C text",
    "option_4": "Option D text",
    "correct_option": "A"
}"""

    user_prompt = "Category: Architecture, Keyword: 한옥"
    
    try:
        print("Testing o4-mini-2025-04-16 API response...")
        response = client.chat.completions.create(
            model="o4-mini-2025-04-16",
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            max_completion_tokens=500
        )
        
        content = response.choices[0].message.content.strip()
        print(f"Raw response: {repr(content)}")
        print(f"Response length: {len(content)}")
        print(f"First 200 chars: {content[:200]}")
        
        # Try to parse as JSON
        try:
            parsed = json.loads(content)
            print("✓ Successfully parsed as JSON")
            print(f"Parsed content: {json.dumps(parsed, indent=2, ensure_ascii=False)}")
        except json.JSONDecodeError as e:
            print(f"✗ JSON parsing failed: {e}")
            
            # Try cleaning the content
            cleaned = content
            if content.startswith('```json'):
                cleaned = content[7:-3].strip()
            elif content.startswith('```'):
                cleaned = content[3:-3].strip()
            
            print(f"Cleaned content: {repr(cleaned)}")
            
            try:
                parsed = json.loads(cleaned)
                print("✓ Successfully parsed cleaned content as JSON")
                print(f"Parsed content: {json.dumps(parsed, indent=2, ensure_ascii=False)}")
            except json.JSONDecodeError as e2:
                print(f"✗ Cleaned JSON parsing also failed: {e2}")
        
    except Exception as e:
        print(f"API call failed: {e}")

if __name__ == "__main__":
    test_api_response()
