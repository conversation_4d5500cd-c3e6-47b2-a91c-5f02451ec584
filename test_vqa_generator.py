#!/usr/bin/env python3
"""
Test script for VQA Generator
Tests basic functionality without making actual API calls
"""

import os
import sys
import tempfile
import csv
from unittest.mock import Mock, patch
import json

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from vqa_generator import VQAGenerator, VQAItem

def create_test_csv():
    """Create a test CSV file"""
    test_data = [
        {
            'Main Category': 'Architecture',
            'Question': '',
            'Option 1': '',
            'Option 2': '',
            'Option 3': '',
            'Option 4': '',
            'Correct Option': '',
            'Image Source': 'https://example.com/test.jpg',
            'Keyword/Concept': '한옥'
        },
        {
            'Main Category': 'Food',
            'Question': '',
            'Option 1': '',
            'Option 2': '',
            'Option 3': '',
            'Option 4': '',
            'Correct Option': '',
            'Image Source': '',
            'Keyword/Concept': '김치'
        },
        {
            'Main Category': '',
            'Question': '',
            'Option 1': '',
            'Option 2': '',
            'Option 3': '',
            'Option 4': '',
            'Correct Option': '',
            'Image Source': '',
            'Keyword/Concept': ''
        }
    ]
    
    # Create temporary CSV file
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8')
    fieldnames = ['Main Category', 'Question', 'Option 1', 'Option 2', 'Option 3', 'Option 4', 'Correct Option', 'Image Source', 'Keyword/Concept']
    
    writer = csv.DictWriter(temp_file, fieldnames=fieldnames)
    writer.writeheader()
    writer.writerows(test_data)
    temp_file.close()
    
    return temp_file.name

def create_test_rules():
    """Create a test rules file"""
    rules_content = """
# Test VQA Generation Rules

## Core Principles
- Generate expert-level questions
- Require multi-hop reasoning
- Focus on Korean cultural knowledge

## Question Requirements
- Must be challenging
- Require cultural expertise
- Include 4 multiple choice options
"""
    
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False, encoding='utf-8')
    temp_file.write(rules_content)
    temp_file.close()
    
    return temp_file.name

def test_initialization():
    """Test VQA Generator initialization"""
    print("Testing VQA Generator initialization...")
    
    rules_file = create_test_rules()
    csv_file = create_test_csv()
    
    try:
        generator = VQAGenerator("test-api-key", rules_file, csv_file)
        assert generator.rules_file == rules_file
        assert generator.csv_file == csv_file
        assert len(generator.generation_rules) > 0
        print("✓ Initialization test passed")
    except Exception as e:
        print(f"✗ Initialization test failed: {e}")
        return False
    finally:
        os.unlink(rules_file)
        os.unlink(csv_file)
    
    return True

def test_url_validation():
    """Test URL validation"""
    print("Testing URL validation...")
    
    rules_file = create_test_rules()
    csv_file = create_test_csv()
    
    try:
        generator = VQAGenerator("test-api-key", rules_file, csv_file)
        
        # Test valid URL format (we can't test actual connectivity without internet)
        valid_url = "https://example.com/image.jpg"
        invalid_urls = ["", "not-a-url", "ftp://example.com", None]
        
        # Note: This will fail for valid URLs due to network, but tests the validation logic
        for url in invalid_urls:
            result = generator._is_valid_image_url(url)
            assert result == False, f"Expected False for invalid URL: {url}"
        
        print("✓ URL validation test passed")
    except Exception as e:
        print(f"✗ URL validation test failed: {e}")
        return False
    finally:
        os.unlink(rules_file)
        os.unlink(csv_file)
    
    return True

def test_vqa_item():
    """Test VQAItem data class"""
    print("Testing VQAItem data class...")
    
    try:
        item = VQAItem(
            main_category="Test Category",
            question="Test question?",
            option_1="Option A",
            option_2="Option B", 
            option_3="Option C",
            option_4="Option D",
            correct_option="A",
            image_source="https://example.com/test.jpg",
            keyword_concept="test keyword"
        )
        
        assert item.main_category == "Test Category"
        assert item.question == "Test question?"
        assert item.correct_option == "A"
        
        print("✓ VQAItem test passed")
    except Exception as e:
        print(f"✗ VQAItem test failed: {e}")
        return False
    
    return True

def test_csv_processing_structure():
    """Test CSV processing structure (without API calls)"""
    print("Testing CSV processing structure...")
    
    rules_file = create_test_rules()
    csv_file = create_test_csv()
    
    try:
        generator = VQAGenerator("test-api-key", rules_file, csv_file)
        
        # Mock the API generation methods to avoid actual API calls
        with patch.object(generator, '_generate_vqa_with_image') as mock_with_image, \
             patch.object(generator, '_generate_vqa_without_image') as mock_without_image, \
             patch.object(generator, '_is_valid_image_url') as mock_url_valid:
            
            # Configure mocks
            mock_url_valid.return_value = True
            mock_with_image.return_value = VQAItem(
                main_category="Architecture",
                question="Mock question with image?",
                option_1="Mock A", option_2="Mock B", option_3="Mock C", option_4="Mock D",
                correct_option="A", image_source="https://example.com/test.jpg", keyword_concept="한옥"
            )
            mock_without_image.return_value = VQAItem(
                main_category="Food",
                question="Mock question without image?",
                option_1="Mock A", option_2="Mock B", option_3="Mock C", option_4="Mock D",
                correct_option="B", image_source="", keyword_concept="김치"
            )
            
            # Process CSV
            results = generator.process_csv()
            
            # Verify results structure
            assert len(results) == 3, f"Expected 3 results, got {len(results)}"
            
            # First row: with image
            assert results[0].main_category == "Architecture"
            assert results[0].question == "Mock question with image?"
            assert results[0].image_source == "https://example.com/test.jpg"
            
            # Second row: without image
            assert results[1].main_category == "Food"
            assert results[1].question == "Mock question without image?"
            assert results[1].image_source == ""
            
            # Third row: empty
            assert results[2].main_category == ""
            assert results[2].question == ""
            
            print("✓ CSV processing structure test passed")
    except Exception as e:
        print(f"✗ CSV processing structure test failed: {e}")
        return False
    finally:
        os.unlink(rules_file)
        os.unlink(csv_file)
    
    return True

def test_output_formats():
    """Test output format generation"""
    print("Testing output formats...")
    
    rules_file = create_test_rules()
    csv_file = create_test_csv()
    
    try:
        generator = VQAGenerator("test-api-key", rules_file, csv_file)
        
        # Create test results
        test_results = [
            VQAItem(
                main_category="Test",
                question="Test question?",
                option_1="A", option_2="B", option_3="C", option_4="D",
                correct_option="A", image_source="", keyword_concept="test"
            )
        ]
        
        # Test CSV output
        csv_output = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False)
        csv_output.close()
        generator._save_to_csv(test_results, csv_output.name)
        
        # Verify CSV output
        with open(csv_output.name, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            rows = list(reader)
            assert len(rows) == 1
            assert rows[0]['Main Category'] == 'Test'
            assert rows[0]['Question'] == 'Test question?'
        
        # Test JSON output
        json_output = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        json_output.close()
        generator._save_to_json(test_results, json_output.name)
        
        # Verify JSON output
        with open(json_output.name, 'r', encoding='utf-8') as f:
            data = json.load(f)
            assert len(data) == 1
            assert data[0]['main_category'] == 'Test'
            assert data[0]['question'] == 'Test question?'
            assert 'options' in data[0]
            assert data[0]['options']['A'] == 'A'
        
        print("✓ Output formats test passed")
        
        # Cleanup
        os.unlink(csv_output.name)
        os.unlink(json_output.name)
        
    except Exception as e:
        print(f"✗ Output formats test failed: {e}")
        return False
    finally:
        os.unlink(rules_file)
        os.unlink(csv_file)
    
    return True

def main():
    """Run all tests"""
    print("Running VQA Generator Tests")
    print("=" * 40)
    
    tests = [
        test_initialization,
        test_url_validation,
        test_vqa_item,
        test_csv_processing_structure,
        test_output_formats
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 40)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("All tests passed! ✓")
        return 0
    else:
        print("Some tests failed! ✗")
        return 1

if __name__ == "__main__":
    exit(main())
