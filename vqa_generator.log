2025-06-19 15:35:19,658 - INFO - Loaded generation rules from /tmp/tmp_gx8mmy8.md
2025-06-19 15:35:19,676 - INFO - Loaded generation rules from /tmp/tmpuqiar16m.md
2025-06-19 15:35:19,676 - WARNING - URL validation failed for ftp://example.com: No connection adapters were found for 'ftp://example.com'
2025-06-19 15:35:19,694 - INFO - Loaded generation rules from /tmp/tmp1_wybxky.md
2025-06-19 15:35:19,694 - INFO - Row 2: Processing Architecture/한옥
2025-06-19 15:35:19,694 - INFO - Row 2: Generating VQA with image
2025-06-19 15:35:19,695 - INFO - Row 2: Successfully generated VQA
2025-06-19 15:35:20,695 - INFO - Row 3: Processing Food/김치
2025-06-19 15:35:20,695 - INFO - Row 3: Generating VQA without image
2025-06-19 15:35:20,695 - INFO - Row 3: Successfully generated VQA
2025-06-19 15:35:21,695 - INFO - Row 4: Skipping empty row
2025-06-19 15:35:21,714 - INFO - Loaded generation rules from /tmp/tmpsx90xrzm.md
2025-06-19 15:35:30,937 - INFO - Loaded generation rules from /tmp/tmpcq7eef5g.md
2025-06-19 15:40:15,172 - INFO - Loaded generation rules from /tmp/tmplha76fx6.md
2025-06-19 15:40:15,190 - INFO - Loaded generation rules from /tmp/tmp2ukk03_2.md
2025-06-19 15:40:15,190 - WARNING - URL validation failed for ftp://example.com: No connection adapters were found for 'ftp://example.com'
2025-06-19 15:40:15,208 - INFO - Loaded generation rules from /tmp/tmpn_5jmgo0.md
2025-06-19 15:40:15,208 - INFO - Row 2: Processing Architecture/한옥
2025-06-19 15:40:15,208 - INFO - Row 2: Generating VQA with image
2025-06-19 15:40:15,208 - INFO - Row 2: Successfully generated VQA
2025-06-19 15:40:16,209 - INFO - Row 3: Processing Food/김치
2025-06-19 15:40:16,209 - INFO - Row 3: Generating VQA without image
2025-06-19 15:40:16,209 - INFO - Row 3: Successfully generated VQA
2025-06-19 15:40:17,209 - INFO - Row 4: Skipping empty row
2025-06-19 15:40:17,228 - INFO - Loaded generation rules from /tmp/tmpsvo157mw.md
2025-06-19 15:45:03,814 - INFO - Initializing VQA Generator...
2025-06-19 15:45:03,833 - INFO - Loaded generation rules from /mnt/raid6/junkim100/east-asia/VQA_Generation_Rules.md
2025-06-19 15:45:03,833 - INFO - Processing CSV file...
2025-06-19 15:45:03,833 - INFO - Row 2: Processing Architecture/제주 돌집
2025-06-19 15:45:04,171 - INFO - Row 2: Generating VQA without image
2025-06-19 15:45:05,107 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-19 15:45:05,108 - ERROR - Error generating VQA without image for Architecture/제주 돌집: Error code: 400 - {'error': {'message': "Unsupported parameter: 'max_tokens' is not supported with this model. Use 'max_completion_tokens' instead.", 'type': 'invalid_request_error', 'param': 'max_tokens', 'code': 'unsupported_parameter'}}
2025-06-19 15:45:05,108 - ERROR - Row 2: Failed to generate VQA
2025-06-19 15:45:06,109 - INFO - Row 3: Processing Architecture/월정교
2025-06-19 15:45:06,311 - INFO - Row 3: Generating VQA without image
2025-06-19 15:45:06,961 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-19 15:45:06,961 - ERROR - Error generating VQA without image for Architecture/월정교: Error code: 400 - {'error': {'message': "Unsupported parameter: 'max_tokens' is not supported with this model. Use 'max_completion_tokens' instead.", 'type': 'invalid_request_error', 'param': 'max_tokens', 'code': 'unsupported_parameter'}}
2025-06-19 15:45:06,961 - ERROR - Row 3: Failed to generate VQA
2025-06-19 15:45:07,963 - INFO - Row 4: Processing Architecture/운현궁
2025-06-19 15:45:08,159 - INFO - Row 4: Generating VQA without image
2025-06-19 15:45:08,742 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-19 15:45:08,742 - ERROR - Error generating VQA without image for Architecture/운현궁: Error code: 400 - {'error': {'message': "Unsupported parameter: 'max_tokens' is not supported with this model. Use 'max_completion_tokens' instead.", 'type': 'invalid_request_error', 'param': 'max_tokens', 'code': 'unsupported_parameter'}}
2025-06-19 15:45:08,743 - ERROR - Row 4: Failed to generate VQA
2025-06-19 15:45:09,743 - INFO - Row 5: Processing Architecture/명동
2025-06-19 15:45:09,808 - INFO - Row 5: Generating VQA with image
2025-06-19 15:45:12,915 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-19 15:45:12,915 - ERROR - Error generating VQA with image for Architecture/명동: Error code: 400 - {'error': {'message': "Unsupported parameter: 'max_tokens' is not supported with this model. Use 'max_completion_tokens' instead.", 'type': 'invalid_request_error', 'param': 'max_tokens', 'code': 'unsupported_parameter'}}
2025-06-19 15:45:12,915 - ERROR - Row 5: Failed to generate VQA
2025-06-19 15:45:13,917 - INFO - Row 6: Processing Architecture/남산타워
2025-06-19 15:45:14,027 - INFO - Generation interrupted by user
2025-06-19 15:49:34,458 - INFO - Loaded generation rules from /tmp/tmpt7blycxx.md
2025-06-19 15:49:34,463 - INFO - Loaded generation rules from /tmp/tmpro4modto.md
2025-06-19 15:49:34,464 - WARNING - URL validation failed for ftp://example.com: No connection adapters were found for 'ftp://example.com'
2025-06-19 15:49:34,469 - INFO - Loaded generation rules from /tmp/tmpgngds4as.md
2025-06-19 15:49:34,469 - INFO - Progress will be saved to: tmpfr0lr60p_vqa_progress.csv
2025-06-19 15:49:34,469 - INFO - Row 2: Processing Architecture/한옥
2025-06-19 15:49:34,469 - INFO - Row 2: Generating VQA with image
2025-06-19 15:49:34,470 - INFO - Row 2: Successfully generated VQA
2025-06-19 15:49:34,470 - INFO - Progress saved: 1 rows completed
2025-06-19 15:49:35,471 - INFO - Row 3: Processing Food/김치
2025-06-19 15:49:35,471 - INFO - Row 3: Generating VQA without image
2025-06-19 15:49:35,471 - INFO - Row 3: Successfully generated VQA
2025-06-19 15:49:35,476 - INFO - Progress saved: 2 rows completed
2025-06-19 15:49:36,477 - INFO - Row 4: Skipping empty row
2025-06-19 15:49:36,483 - INFO - Loaded generation rules from /tmp/tmp6y3pd4gp.md
2025-06-19 15:53:38,159 - INFO - Initializing VQA Generator...
2025-06-19 15:53:38,177 - INFO - Loaded generation rules from /mnt/raid6/junkim100/east-asia/VQA_Generation_Rules.md
2025-06-19 15:53:38,177 - INFO - Processing CSV file...
2025-06-19 15:53:38,177 - INFO - Progress will be saved to: VQA _vqa_progress.csv
2025-06-19 15:53:38,177 - INFO - Row 2: Processing Architecture/제주 돌집
2025-06-19 15:53:38,576 - INFO - Row 2: Generating VQA without image
2025-06-19 15:53:39,155 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-19 15:53:39,156 - ERROR - Error generating VQA without image for Architecture/제주 돌집: Error code: 400 - {'error': {'message': "Unsupported value: 'temperature' does not support 0.7 with this model. Only the default (1) value is supported.", 'type': 'invalid_request_error', 'param': 'temperature', 'code': 'unsupported_value'}}
2025-06-19 15:53:39,156 - ERROR - Row 2: Failed to generate VQA
2025-06-19 15:53:39,156 - INFO - Progress saved: 1 rows completed
2025-06-19 15:53:40,158 - INFO - Row 3: Processing Architecture/월정교
2025-06-19 15:53:40,368 - INFO - Row 3: Generating VQA without image
2025-06-19 15:53:40,759 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-19 15:53:40,760 - ERROR - Error generating VQA without image for Architecture/월정교: Error code: 400 - {'error': {'message': "Unsupported value: 'temperature' does not support 0.7 with this model. Only the default (1) value is supported.", 'type': 'invalid_request_error', 'param': 'temperature', 'code': 'unsupported_value'}}
2025-06-19 15:53:40,760 - ERROR - Row 3: Failed to generate VQA
2025-06-19 15:53:40,765 - INFO - Progress saved: 2 rows completed
2025-06-19 15:53:41,766 - INFO - Row 4: Processing Architecture/운현궁
2025-06-19 15:53:41,997 - INFO - Row 4: Generating VQA without image
2025-06-19 15:53:42,351 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-19 15:53:42,353 - ERROR - Error generating VQA without image for Architecture/운현궁: Error code: 400 - {'error': {'message': "Unsupported value: 'temperature' does not support 0.7 with this model. Only the default (1) value is supported.", 'type': 'invalid_request_error', 'param': 'temperature', 'code': 'unsupported_value'}}
2025-06-19 15:53:42,353 - ERROR - Row 4: Failed to generate VQA
2025-06-19 15:53:42,354 - INFO - Progress saved: 3 rows completed
2025-06-19 15:53:43,355 - INFO - Row 5: Processing Architecture/명동
2025-06-19 15:53:43,424 - INFO - Row 5: Generating VQA with image
2025-06-19 15:53:43,425 - INFO - Downloading image from URL: https://images.unsplash.com/photo-1677097610167-c2d62b06fad3?q=80&w=2670&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D
2025-06-19 15:53:43,644 - INFO - Resized image to (2048, 1365)
2025-06-19 15:53:43,657 - INFO - Successfully processed image, size: 651189 bytes
2025-06-19 15:53:44,539 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-19 15:53:44,539 - ERROR - Error generating VQA with image for Architecture/명동: Error code: 400 - {'error': {'message': "Unsupported value: 'temperature' does not support 0.7 with this model. Only the default (1) value is supported.", 'type': 'invalid_request_error', 'param': 'temperature', 'code': 'unsupported_value'}}
2025-06-19 15:53:44,539 - ERROR - Row 5: Failed to generate VQA
2025-06-19 15:53:44,540 - INFO - Progress saved: 4 rows completed
2025-06-19 15:53:45,541 - INFO - Row 6: Processing Architecture/남산타워
2025-06-19 15:53:45,856 - INFO - Row 6: Generating VQA without image
2025-06-19 15:53:46,289 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-19 15:53:46,290 - ERROR - Error generating VQA without image for Architecture/남산타워: Error code: 400 - {'error': {'message': "Unsupported value: 'temperature' does not support 0.7 with this model. Only the default (1) value is supported.", 'type': 'invalid_request_error', 'param': 'temperature', 'code': 'unsupported_value'}}
2025-06-19 15:53:46,290 - ERROR - Row 6: Failed to generate VQA
2025-06-19 15:53:46,291 - INFO - Progress saved: 5 rows completed
2025-06-19 15:53:47,293 - INFO - Row 7: Processing Architecture/신라대종
2025-06-19 15:53:47,529 - INFO - Row 7: Generating VQA without image
2025-06-19 15:53:48,531 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-19 15:53:48,532 - ERROR - Error generating VQA without image for Architecture/신라대종: Error code: 400 - {'error': {'message': "Unsupported value: 'temperature' does not support 0.7 with this model. Only the default (1) value is supported.", 'type': 'invalid_request_error', 'param': 'temperature', 'code': 'unsupported_value'}}
2025-06-19 15:53:48,532 - ERROR - Row 7: Failed to generate VQA
2025-06-19 15:53:48,532 - INFO - Progress saved: 6 rows completed
2025-06-19 15:53:49,533 - INFO - Row 8: Processing Architecture/고려대학교
2025-06-19 15:53:49,881 - INFO - Row 8: Generating VQA without image
2025-06-19 15:53:50,254 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-19 15:53:50,254 - ERROR - Error generating VQA without image for Architecture/고려대학교: Error code: 400 - {'error': {'message': "Unsupported value: 'temperature' does not support 0.7 with this model. Only the default (1) value is supported.", 'type': 'invalid_request_error', 'param': 'temperature', 'code': 'unsupported_value'}}
2025-06-19 15:53:50,254 - ERROR - Row 8: Failed to generate VQA
2025-06-19 15:53:50,255 - INFO - Progress saved: 7 rows completed
2025-06-19 15:53:51,256 - INFO - Row 9: Processing Architecture/한강다리
2025-06-19 15:53:52,779 - INFO - Row 9: Generating VQA with image
2025-06-19 15:53:52,779 - INFO - Downloading image from URL: https://plus.unsplash.com/premium_photo-1716968594404-ac5ae8cdcdc4?q=80&w=2667&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D
2025-06-19 15:53:52,951 - INFO - Resized image to (2048, 1370)
2025-06-19 15:53:52,959 - INFO - Successfully processed image, size: 342656 bytes
2025-06-19 15:53:54,406 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-19 15:53:54,407 - ERROR - Error generating VQA with image for Architecture/한강다리: Error code: 400 - {'error': {'message': "Unsupported value: 'temperature' does not support 0.7 with this model. Only the default (1) value is supported.", 'type': 'invalid_request_error', 'param': 'temperature', 'code': 'unsupported_value'}}
2025-06-19 15:53:54,407 - ERROR - Row 9: Failed to generate VQA
2025-06-19 15:53:54,408 - INFO - Progress saved: 8 rows completed
2025-06-19 15:53:55,409 - INFO - Row 10: Processing Architecture/DDP
2025-06-19 15:53:55,829 - INFO - Row 10: Generating VQA without image
2025-06-19 15:53:56,565 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-19 15:53:56,565 - ERROR - Error generating VQA without image for Architecture/DDP: Error code: 400 - {'error': {'message': "Unsupported value: 'temperature' does not support 0.7 with this model. Only the default (1) value is supported.", 'type': 'invalid_request_error', 'param': 'temperature', 'code': 'unsupported_value'}}
2025-06-19 15:53:56,565 - ERROR - Row 10: Failed to generate VQA
2025-06-19 15:53:56,566 - INFO - Progress saved: 9 rows completed
2025-06-19 15:53:57,567 - INFO - Row 11: Processing Architecture/탑골공원
2025-06-19 15:53:57,567 - INFO - Row 11: Generating VQA without image
2025-06-19 15:53:58,157 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-19 15:53:58,158 - ERROR - Error generating VQA without image for Architecture/탑골공원: Error code: 400 - {'error': {'message': "Unsupported value: 'temperature' does not support 0.7 with this model. Only the default (1) value is supported.", 'type': 'invalid_request_error', 'param': 'temperature', 'code': 'unsupported_value'}}
2025-06-19 15:53:58,158 - ERROR - Row 11: Failed to generate VQA
2025-06-19 15:53:58,158 - INFO - Progress saved: 10 rows completed
2025-06-19 15:53:59,159 - INFO - Row 12: Processing Architecture/PC방
2025-06-19 15:53:59,469 - INFO - Row 12: Generating VQA without image
2025-06-19 15:53:59,864 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-19 15:53:59,865 - ERROR - Error generating VQA without image for Architecture/PC방: Error code: 400 - {'error': {'message': "Unsupported value: 'temperature' does not support 0.7 with this model. Only the default (1) value is supported.", 'type': 'invalid_request_error', 'param': 'temperature', 'code': 'unsupported_value'}}
2025-06-19 15:53:59,865 - ERROR - Row 12: Failed to generate VQA
2025-06-19 15:53:59,865 - INFO - Progress saved: 11 rows completed
2025-06-19 15:54:00,866 - INFO - Row 13: Processing Architecture/분식집
2025-06-19 15:54:01,488 - INFO - Row 13: Generating VQA with image
2025-06-19 15:54:01,489 - INFO - Downloading image from URL: https://www.google.com/url?sa=i&url=https%3A%2F%2Fcommons.wikimedia.org%2Fwiki%2FFile%3A2020-03-11_12.23.44_%25EB%25B6%2584%25EC%258B%259D%25EC%25A7%2591.jpg&psig=AOvVaw2N_hZVLVNCU01ca_oOVZv9&ust=1750399946076000&source=images&cd=vfe&opi=89978449&ved=0CBQQjRxqFwoTCJDTtonq_I0DFQAAAAAdAAAAABAI
2025-06-19 15:54:02,081 - ERROR - Failed to download/encode image from https://www.google.com/url?sa=i&url=https%3A%2F%2Fcommons.wikimedia.org%2Fwiki%2FFile%3A2020-03-11_12.23.44_%25EB%25B6%2584%25EC%258B%259D%25EC%25A7%2591.jpg&psig=AOvVaw2N_hZVLVNCU01ca_oOVZv9&ust=1750399946076000&source=images&cd=vfe&opi=89978449&ved=0CBQQjRxqFwoTCJDTtonq_I0DFQAAAAAdAAAAABAI: cannot identify image file <_io.BytesIO object at 0x725e9dd82cf0>
2025-06-19 15:54:02,082 - ERROR - Failed to process image from https://www.google.com/url?sa=i&url=https%3A%2F%2Fcommons.wikimedia.org%2Fwiki%2FFile%3A2020-03-11_12.23.44_%25EB%25B6%2584%25EC%258B%259D%25EC%25A7%2591.jpg&psig=AOvVaw2N_hZVLVNCU01ca_oOVZv9&ust=1750399946076000&source=images&cd=vfe&opi=89978449&ved=0CBQQjRxqFwoTCJDTtonq_I0DFQAAAAAdAAAAABAI
2025-06-19 15:54:02,082 - ERROR - Row 13: Failed to generate VQA
2025-06-19 15:54:02,082 - INFO - Progress saved: 12 rows completed
2025-06-19 15:54:03,084 - INFO - Row 14: Processing Architecture/빵집
2025-06-19 15:54:03,806 - INFO - Row 14: Generating VQA without image
2025-06-19 15:54:04,177 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-19 15:54:04,178 - ERROR - Error generating VQA without image for Architecture/빵집: Error code: 400 - {'error': {'message': "Unsupported value: 'temperature' does not support 0.7 with this model. Only the default (1) value is supported.", 'type': 'invalid_request_error', 'param': 'temperature', 'code': 'unsupported_value'}}
2025-06-19 15:54:04,178 - ERROR - Row 14: Failed to generate VQA
2025-06-19 15:54:04,178 - INFO - Progress saved: 13 rows completed
2025-06-19 15:54:05,179 - INFO - Row 15: Processing Architecture/광화문
2025-06-19 15:54:05,398 - INFO - Row 15: Generating VQA with image
2025-06-19 15:54:05,398 - INFO - Downloading image from URL: https://images.unsplash.com/photo-1615428277562-f2dd4b887de2?q=80&w=2670&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D
2025-06-19 15:54:05,615 - INFO - Resized image to (2048, 1365)
2025-06-19 15:54:05,624 - INFO - Successfully processed image, size: 791122 bytes
2025-06-19 15:54:06,574 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-19 15:54:06,575 - ERROR - Error generating VQA with image for Architecture/광화문: Error code: 400 - {'error': {'message': "Unsupported value: 'temperature' does not support 0.7 with this model. Only the default (1) value is supported.", 'type': 'invalid_request_error', 'param': 'temperature', 'code': 'unsupported_value'}}
2025-06-19 15:54:06,575 - ERROR - Row 15: Failed to generate VQA
2025-06-19 15:54:06,576 - INFO - Progress saved: 14 rows completed
2025-06-19 15:54:07,577 - INFO - Row 16: Processing Architecture/대형마트
2025-06-19 15:54:07,982 - INFO - Row 16: Generating VQA without image
2025-06-19 15:54:08,352 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-19 15:54:08,353 - ERROR - Error generating VQA without image for Architecture/대형마트: Error code: 400 - {'error': {'message': "Unsupported value: 'temperature' does not support 0.7 with this model. Only the default (1) value is supported.", 'type': 'invalid_request_error', 'param': 'temperature', 'code': 'unsupported_value'}}
2025-06-19 15:54:08,353 - ERROR - Row 16: Failed to generate VQA
2025-06-19 15:54:08,354 - INFO - Progress saved: 15 rows completed
2025-06-19 15:54:09,355 - INFO - Row 17: Processing Architecture/떡집
2025-06-19 15:54:09,500 - INFO - Row 17: Generating VQA with image
2025-06-19 15:54:09,500 - INFO - Downloading image from URL: https://www.shutterstock.com/shutterstock/photos/709396000/display_1500/stock-photo-chung-cake-on-altar-in-old-village-communal-house-cooked-square-glutinous-rice-cake-vietnamese-709396000.jpg
2025-06-19 15:54:09,572 - INFO - Successfully processed image, size: 273396 bytes
2025-06-19 15:54:10,120 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-19 15:54:10,121 - ERROR - Error generating VQA with image for Architecture/떡집: Error code: 400 - {'error': {'message': "Unsupported value: 'temperature' does not support 0.7 with this model. Only the default (1) value is supported.", 'type': 'invalid_request_error', 'param': 'temperature', 'code': 'unsupported_value'}}
2025-06-19 15:54:10,121 - ERROR - Row 17: Failed to generate VQA
2025-06-19 15:54:10,121 - INFO - Progress saved: 16 rows completed
2025-06-19 15:54:11,122 - INFO - Row 18: Processing Architecture/고기집
2025-06-19 15:54:11,526 - INFO - Row 18: Generating VQA without image
2025-06-19 15:54:11,717 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-19 15:54:11,718 - ERROR - Error generating VQA without image for Architecture/고기집: Error code: 400 - {'error': {'message': "Unsupported value: 'temperature' does not support 0.7 with this model. Only the default (1) value is supported.", 'type': 'invalid_request_error', 'param': 'temperature', 'code': 'unsupported_value'}}
2025-06-19 15:54:11,718 - ERROR - Row 18: Failed to generate VQA
2025-06-19 15:54:11,719 - INFO - Progress saved: 17 rows completed
2025-06-19 15:54:12,721 - INFO - Row 19: Processing Architecture/찌개집
2025-06-19 15:54:12,721 - INFO - Row 19: Generating VQA without image
2025-06-19 15:54:12,914 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-19 15:54:12,915 - ERROR - Error generating VQA without image for Architecture/찌개집: Error code: 400 - {'error': {'message': "Unsupported value: 'temperature' does not support 0.7 with this model. Only the default (1) value is supported.", 'type': 'invalid_request_error', 'param': 'temperature', 'code': 'unsupported_value'}}
2025-06-19 15:54:12,915 - ERROR - Row 19: Failed to generate VQA
2025-06-19 15:54:12,916 - INFO - Progress saved: 18 rows completed
2025-06-19 15:54:13,917 - INFO - Row 20: Processing Architecture/국밥집
2025-06-19 15:54:13,917 - INFO - Row 20: Generating VQA without image
2025-06-19 15:54:14,329 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-19 15:54:14,329 - ERROR - Error generating VQA without image for Architecture/국밥집: Error code: 400 - {'error': {'message': "Unsupported value: 'temperature' does not support 0.7 with this model. Only the default (1) value is supported.", 'type': 'invalid_request_error', 'param': 'temperature', 'code': 'unsupported_value'}}
2025-06-19 15:54:14,330 - ERROR - Row 20: Failed to generate VQA
2025-06-19 15:54:14,330 - INFO - Progress saved: 19 rows completed
2025-06-19 15:54:15,331 - INFO - Row 21: Processing Architecture/해동용궁사
2025-06-19 15:54:15,527 - INFO - Row 21: Generating VQA without image
2025-06-19 15:54:15,877 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-19 15:54:15,878 - ERROR - Error generating VQA without image for Architecture/해동용궁사: Error code: 400 - {'error': {'message': "Unsupported value: 'temperature' does not support 0.7 with this model. Only the default (1) value is supported.", 'type': 'invalid_request_error', 'param': 'temperature', 'code': 'unsupported_value'}}
2025-06-19 15:54:15,878 - ERROR - Row 21: Failed to generate VQA
2025-06-19 15:54:15,879 - INFO - Progress saved: 20 rows completed
2025-06-19 15:54:16,880 - INFO - Row 22: Processing Architecture/재래시장
2025-06-19 15:54:16,880 - INFO - Row 22: Generating VQA without image
2025-06-19 15:54:17,265 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-19 15:54:17,265 - ERROR - Error generating VQA without image for Architecture/재래시장: Error code: 400 - {'error': {'message': "Unsupported value: 'temperature' does not support 0.7 with this model. Only the default (1) value is supported.", 'type': 'invalid_request_error', 'param': 'temperature', 'code': 'unsupported_value'}}
2025-06-19 15:54:17,265 - ERROR - Row 22: Failed to generate VQA
2025-06-19 15:54:17,266 - INFO - Progress saved: 21 rows completed
2025-06-19 15:54:18,267 - INFO - Row 23: Processing Architecture/한옥마을
2025-06-19 15:54:18,605 - INFO - Row 23: Generating VQA without image
2025-06-19 15:54:18,799 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-19 15:54:18,800 - ERROR - Error generating VQA without image for Architecture/한옥마을: Error code: 400 - {'error': {'message': "Unsupported value: 'temperature' does not support 0.7 with this model. Only the default (1) value is supported.", 'type': 'invalid_request_error', 'param': 'temperature', 'code': 'unsupported_value'}}
2025-06-19 15:54:18,800 - ERROR - Row 23: Failed to generate VQA
2025-06-19 15:54:18,801 - INFO - Progress saved: 22 rows completed
2025-06-19 15:54:19,802 - INFO - Row 24: Processing Architecture/수원화성
2025-06-19 15:54:20,054 - INFO - Row 24: Generating VQA without image
2025-06-19 15:54:20,417 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-19 15:54:20,418 - ERROR - Error generating VQA without image for Architecture/수원화성: Error code: 400 - {'error': {'message': "Unsupported value: 'temperature' does not support 0.7 with this model. Only the default (1) value is supported.", 'type': 'invalid_request_error', 'param': 'temperature', 'code': 'unsupported_value'}}
2025-06-19 15:54:20,418 - ERROR - Row 24: Failed to generate VQA
2025-06-19 15:54:20,419 - INFO - Progress saved: 23 rows completed
2025-06-19 15:54:21,420 - INFO - Row 25: Processing Architecture/경희궁
2025-06-19 15:54:21,697 - INFO - Row 25: Generating VQA without image
2025-06-19 15:54:22,076 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-19 15:54:22,077 - ERROR - Error generating VQA without image for Architecture/경희궁: Error code: 400 - {'error': {'message': "Unsupported value: 'temperature' does not support 0.7 with this model. Only the default (1) value is supported.", 'type': 'invalid_request_error', 'param': 'temperature', 'code': 'unsupported_value'}}
2025-06-19 15:54:22,077 - ERROR - Row 25: Failed to generate VQA
2025-06-19 15:54:22,078 - INFO - Progress saved: 24 rows completed
2025-06-19 15:54:23,079 - INFO - Row 26: Processing Architecture/종묘
2025-06-19 15:54:23,923 - INFO - Row 26: Generating VQA with image
2025-06-19 15:54:23,923 - INFO - Downloading image from URL: https://www.shutterstock.com/shutterstock/photos/641626096/display_1500/stock-photo-jongmyo-shrine-in-seoul-south-korea-is-a-unesco-world-heritage-site-641626096.jpg
2025-06-19 15:54:24,002 - INFO - Successfully processed image, size: 426273 bytes
2025-06-19 15:54:24,719 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-19 15:54:24,719 - ERROR - Error generating VQA with image for Architecture/종묘: Error code: 400 - {'error': {'message': "Unsupported value: 'temperature' does not support 0.7 with this model. Only the default (1) value is supported.", 'type': 'invalid_request_error', 'param': 'temperature', 'code': 'unsupported_value'}}
2025-06-19 15:54:24,720 - ERROR - Row 26: Failed to generate VQA
2025-06-19 15:54:24,720 - INFO - Progress saved: 25 rows completed
2025-06-19 15:54:25,721 - INFO - Row 27: Processing Architecture/독립문
2025-06-19 15:54:26,140 - INFO - Row 27: Generating VQA with image
2025-06-19 15:54:26,140 - INFO - Downloading image from URL: https://cdn.crowdpic.net/detail-thumb/thumb_d_7311445FB05DF2AF814EC82322039DB5.jpg
2025-06-19 15:54:26,252 - INFO - Successfully processed image, size: 73263 bytes
