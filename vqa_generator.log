2025-06-19 16:11:53,063 - INFO - Initializing VQA Generator...
2025-06-19 16:11:53,081 - INFO - Loaded generation rules from /mnt/raid6/junkim100/east-asia/VQA_Generation_Rules.md
2025-06-19 16:11:53,081 - INFO - Processing CSV file...
2025-06-19 16:11:53,081 - INFO - Progress will be saved to: VQA _vqa_progress.csv
2025-06-19 16:11:53,081 - INFO - Row 2: Processing Architecture/제주 돌집
2025-06-19 16:11:53,081 - INFO - Accepting image URL: https://www.notion.so/image/attachment%3A619b2f68-f70e-435a-b659-b51be324c20a%3AScreenshot_2025-05-2...
2025-06-19 16:11:53,082 - INFO - Row 2: Generating VQA with image
2025-06-19 16:11:53,082 - INFO - Downloading image from URL: https://www.notion.so/image/attachment%3A619b2f68-f70e-435a-b659-b51be324c20a%3AScreenshot_2025-05-23_at_2.47.10_PM.png?table=block&id=1fc21dda-bbe5-8146-904f-f3d7e20dda3f&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:11:53,350 - ERROR - Failed to download/encode image from https://www.notion.so/image/attachment%3A619b2f68-f70e-435a-b659-b51be324c20a%3AScreenshot_2025-05-23_at_2.47.10_PM.png?table=block&id=1fc21dda-bbe5-8146-904f-f3d7e20dda3f&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2: 401 Client Error: Unauthorized for url: https://www.notion.so/image/attachment%3A619b2f68-f70e-435a-b659-b51be324c20a%3AScreenshot_2025-05-23_at_2.47.10_PM.png?table=block&id=1fc21dda-bbe5-8146-904f-f3d7e20dda3f&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:11:53,350 - ERROR - Failed to process image from https://www.notion.so/image/attachment%3A619b2f68-f70e-435a-b659-b51be324c20a%3AScreenshot_2025-05-23_at_2.47.10_PM.png?table=block&id=1fc21dda-bbe5-8146-904f-f3d7e20dda3f&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:11:53,350 - ERROR - Row 2: Failed to generate VQA
2025-06-19 16:11:53,351 - INFO - Progress saved: 1 rows completed
2025-06-19 16:11:54,352 - INFO - Row 3: Processing Architecture/월정교
2025-06-19 16:11:54,352 - INFO - Accepting image URL: https://www.notion.so/image/attachment%3A52ff9a7c-cf16-4f4a-baee-a84a4592cfff%3AIMG_0656.jpeg?table=...
2025-06-19 16:11:54,352 - INFO - Row 3: Generating VQA with image
2025-06-19 16:11:54,352 - INFO - Downloading image from URL: https://www.notion.so/image/attachment%3A52ff9a7c-cf16-4f4a-baee-a84a4592cfff%3AIMG_0656.jpeg?table=block&id=1fc21dda-bbe5-815c-8c4f-fbdd92a718eb&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:11:54,619 - ERROR - Failed to download/encode image from https://www.notion.so/image/attachment%3A52ff9a7c-cf16-4f4a-baee-a84a4592cfff%3AIMG_0656.jpeg?table=block&id=1fc21dda-bbe5-815c-8c4f-fbdd92a718eb&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2: 401 Client Error: Unauthorized for url: https://www.notion.so/image/attachment%3A52ff9a7c-cf16-4f4a-baee-a84a4592cfff%3AIMG_0656.jpeg?table=block&id=1fc21dda-bbe5-815c-8c4f-fbdd92a718eb&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:11:54,619 - ERROR - Failed to process image from https://www.notion.so/image/attachment%3A52ff9a7c-cf16-4f4a-baee-a84a4592cfff%3AIMG_0656.jpeg?table=block&id=1fc21dda-bbe5-815c-8c4f-fbdd92a718eb&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:11:54,619 - ERROR - Row 3: Failed to generate VQA
2025-06-19 16:11:54,620 - INFO - Progress saved: 2 rows completed
2025-06-19 16:11:55,621 - INFO - Row 4: Processing Architecture/운현궁
2025-06-19 16:11:55,621 - INFO - Accepting image URL: https://www.notion.so/image/attachment%3Ae997452e-ac86-4583-9980-de73b2c475ca%3AIMG_1269.jpeg?table=...
2025-06-19 16:11:55,621 - INFO - Row 4: Generating VQA with image
2025-06-19 16:11:55,621 - INFO - Downloading image from URL: https://www.notion.so/image/attachment%3Ae997452e-ac86-4583-9980-de73b2c475ca%3AIMG_1269.jpeg?table=block&id=1fc21dda-bbe5-813b-afc9-fd73a8b1fe3b&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:11:55,899 - ERROR - Failed to download/encode image from https://www.notion.so/image/attachment%3Ae997452e-ac86-4583-9980-de73b2c475ca%3AIMG_1269.jpeg?table=block&id=1fc21dda-bbe5-813b-afc9-fd73a8b1fe3b&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2: 401 Client Error: Unauthorized for url: https://www.notion.so/image/attachment%3Ae997452e-ac86-4583-9980-de73b2c475ca%3AIMG_1269.jpeg?table=block&id=1fc21dda-bbe5-813b-afc9-fd73a8b1fe3b&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:11:55,899 - ERROR - Failed to process image from https://www.notion.so/image/attachment%3Ae997452e-ac86-4583-9980-de73b2c475ca%3AIMG_1269.jpeg?table=block&id=1fc21dda-bbe5-813b-afc9-fd73a8b1fe3b&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:11:55,899 - ERROR - Row 4: Failed to generate VQA
2025-06-19 16:11:55,899 - INFO - Progress saved: 3 rows completed
2025-06-19 16:11:56,901 - INFO - Row 5: Processing Architecture/명동
2025-06-19 16:11:56,901 - INFO - Accepting image URL: https://images.unsplash.com/photo-1677097610167-c2d62b06fad3?q=80&w=2670&auto=format&fit=crop&ixlib=...
2025-06-19 16:11:56,901 - INFO - Row 5: Generating VQA with image
2025-06-19 16:11:56,901 - INFO - Downloading image from URL: https://images.unsplash.com/photo-1677097610167-c2d62b06fad3?q=80&w=2670&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D
2025-06-19 16:11:57,439 - INFO - Resized image to (2048, 1365)
2025-06-19 16:11:57,450 - INFO - Successfully processed image, size: 651189 bytes
2025-06-19 16:12:09,535 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 16:12:09,539 - INFO - Raw API response type: <class 'str'>
2025-06-19 16:12:09,539 - INFO - Raw API response is None: False
2025-06-19 16:12:09,539 - INFO - Content length after strip: 0
2025-06-19 16:12:09,539 - INFO - Raw API response: ''...
2025-06-19 16:12:09,539 - INFO - FULL API response: ''
2025-06-19 16:12:09,539 - INFO - Cleaned content for JSON parsing: ''...
2025-06-19 16:12:09,539 - ERROR - Content is empty after cleaning
2025-06-19 16:12:09,539 - ERROR - Row 5: Failed to generate VQA
2025-06-19 16:12:09,540 - INFO - Progress saved: 4 rows completed
2025-06-19 16:12:10,541 - INFO - Row 6: Processing Architecture/남산타워
2025-06-19 16:12:10,541 - INFO - Accepting image URL: https://www.notion.so/image/attachment%3A1047482e-93e8-4f25-9590-52f3dfd1baa8%3AIMG_1727.jpeg?table=...
2025-06-19 16:12:10,541 - INFO - Row 6: Generating VQA with image
2025-06-19 16:12:10,541 - INFO - Downloading image from URL: https://www.notion.so/image/attachment%3A1047482e-93e8-4f25-9590-52f3dfd1baa8%3AIMG_1727.jpeg?table=block&id=1fc21dda-bbe5-81d2-a305-e446ddc791f2&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:12:10,727 - ERROR - Failed to download/encode image from https://www.notion.so/image/attachment%3A1047482e-93e8-4f25-9590-52f3dfd1baa8%3AIMG_1727.jpeg?table=block&id=1fc21dda-bbe5-81d2-a305-e446ddc791f2&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2: 401 Client Error: Unauthorized for url: https://www.notion.so/image/attachment%3A1047482e-93e8-4f25-9590-52f3dfd1baa8%3AIMG_1727.jpeg?table=block&id=1fc21dda-bbe5-81d2-a305-e446ddc791f2&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:12:10,727 - ERROR - Failed to process image from https://www.notion.so/image/attachment%3A1047482e-93e8-4f25-9590-52f3dfd1baa8%3AIMG_1727.jpeg?table=block&id=1fc21dda-bbe5-81d2-a305-e446ddc791f2&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:12:10,727 - ERROR - Row 6: Failed to generate VQA
2025-06-19 16:12:10,728 - INFO - Progress saved: 5 rows completed
2025-06-19 16:12:11,730 - INFO - Row 7: Processing Architecture/신라대종
2025-06-19 16:12:11,730 - INFO - Accepting image URL: https://www.notion.so/image/attachment%3A18f7bd18-ce33-488c-9e30-e7f153449bb9%3AIMG_0587.jpeg?table=...
2025-06-19 16:12:11,730 - INFO - Row 7: Generating VQA with image
2025-06-19 16:12:11,730 - INFO - Downloading image from URL: https://www.notion.so/image/attachment%3A18f7bd18-ce33-488c-9e30-e7f153449bb9%3AIMG_0587.jpeg?table=block&id=1fc21dda-bbe5-81ee-b974-f4ef3de73ceb&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:12:11,997 - ERROR - Failed to download/encode image from https://www.notion.so/image/attachment%3A18f7bd18-ce33-488c-9e30-e7f153449bb9%3AIMG_0587.jpeg?table=block&id=1fc21dda-bbe5-81ee-b974-f4ef3de73ceb&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2: 401 Client Error: Unauthorized for url: https://www.notion.so/image/attachment%3A18f7bd18-ce33-488c-9e30-e7f153449bb9%3AIMG_0587.jpeg?table=block&id=1fc21dda-bbe5-81ee-b974-f4ef3de73ceb&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:12:11,998 - ERROR - Failed to process image from https://www.notion.so/image/attachment%3A18f7bd18-ce33-488c-9e30-e7f153449bb9%3AIMG_0587.jpeg?table=block&id=1fc21dda-bbe5-81ee-b974-f4ef3de73ceb&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:12:11,998 - ERROR - Row 7: Failed to generate VQA
2025-06-19 16:12:11,998 - INFO - Progress saved: 6 rows completed
2025-06-19 16:12:12,999 - INFO - Row 8: Processing Architecture/고려대학교
2025-06-19 16:12:12,999 - INFO - Accepting image URL: https://www.notion.so/image/attachment%3Aadaf8ea1-e599-40a0-89d5-c292ee2780fd%3AIMG_3619.jpeg?table=...
2025-06-19 16:12:13,000 - INFO - Row 8: Generating VQA with image
2025-06-19 16:12:13,000 - INFO - Downloading image from URL: https://www.notion.so/image/attachment%3Aadaf8ea1-e599-40a0-89d5-c292ee2780fd%3AIMG_3619.jpeg?table=block&id=1fc21dda-bbe5-81fe-98d5-e8850a9f2ba7&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:12:13,210 - ERROR - Failed to download/encode image from https://www.notion.so/image/attachment%3Aadaf8ea1-e599-40a0-89d5-c292ee2780fd%3AIMG_3619.jpeg?table=block&id=1fc21dda-bbe5-81fe-98d5-e8850a9f2ba7&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2: 401 Client Error: Unauthorized for url: https://www.notion.so/image/attachment%3Aadaf8ea1-e599-40a0-89d5-c292ee2780fd%3AIMG_3619.jpeg?table=block&id=1fc21dda-bbe5-81fe-98d5-e8850a9f2ba7&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:12:13,210 - ERROR - Failed to process image from https://www.notion.so/image/attachment%3Aadaf8ea1-e599-40a0-89d5-c292ee2780fd%3AIMG_3619.jpeg?table=block&id=1fc21dda-bbe5-81fe-98d5-e8850a9f2ba7&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:12:13,210 - ERROR - Row 8: Failed to generate VQA
2025-06-19 16:12:13,211 - INFO - Progress saved: 7 rows completed
2025-06-19 16:12:14,212 - INFO - Row 9: Processing Architecture/한강다리
2025-06-19 16:12:14,212 - INFO - Accepting image URL: https://plus.unsplash.com/premium_photo-1716968594404-ac5ae8cdcdc4?q=80&w=2667&auto=format&fit=crop&...
2025-06-19 16:12:14,212 - INFO - Row 9: Generating VQA with image
2025-06-19 16:12:14,212 - INFO - Downloading image from URL: https://plus.unsplash.com/premium_photo-1716968594404-ac5ae8cdcdc4?q=80&w=2667&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D
2025-06-19 16:12:14,529 - INFO - Resized image to (2048, 1370)
2025-06-19 16:12:14,537 - INFO - Successfully processed image, size: 342656 bytes
2025-06-19 16:12:28,052 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 16:12:28,054 - INFO - Raw API response type: <class 'str'>
2025-06-19 16:12:28,054 - INFO - Raw API response is None: False
2025-06-19 16:12:28,054 - INFO - Content length after strip: 0
2025-06-19 16:12:28,055 - INFO - Raw API response: ''...
2025-06-19 16:12:28,055 - INFO - FULL API response: ''
2025-06-19 16:12:28,055 - INFO - Cleaned content for JSON parsing: ''...
2025-06-19 16:12:28,055 - ERROR - Content is empty after cleaning
2025-06-19 16:12:28,055 - ERROR - Row 9: Failed to generate VQA
2025-06-19 16:12:28,056 - INFO - Progress saved: 8 rows completed
2025-06-19 16:12:29,057 - INFO - Row 10: Processing Architecture/DDP
2025-06-19 16:12:29,058 - INFO - Accepting image URL: https://www.notion.so/image/attachment%3Afae18d60-ca9f-4e84-aab6-d4e76c22b174%3AIMG_1324.jpeg?table=...
2025-06-19 16:12:29,058 - INFO - Row 10: Generating VQA with image
2025-06-19 16:12:29,058 - INFO - Downloading image from URL: https://www.notion.so/image/attachment%3Afae18d60-ca9f-4e84-aab6-d4e76c22b174%3AIMG_1324.jpeg?table=block&id=1fc21dda-bbe5-815c-98ad-e69190909ffe&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:12:29,366 - ERROR - Failed to download/encode image from https://www.notion.so/image/attachment%3Afae18d60-ca9f-4e84-aab6-d4e76c22b174%3AIMG_1324.jpeg?table=block&id=1fc21dda-bbe5-815c-98ad-e69190909ffe&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2: 401 Client Error: Unauthorized for url: https://www.notion.so/image/attachment%3Afae18d60-ca9f-4e84-aab6-d4e76c22b174%3AIMG_1324.jpeg?table=block&id=1fc21dda-bbe5-815c-98ad-e69190909ffe&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:12:29,366 - ERROR - Failed to process image from https://www.notion.so/image/attachment%3Afae18d60-ca9f-4e84-aab6-d4e76c22b174%3AIMG_1324.jpeg?table=block&id=1fc21dda-bbe5-815c-98ad-e69190909ffe&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:12:29,366 - ERROR - Row 10: Failed to generate VQA
2025-06-19 16:12:29,366 - INFO - Progress saved: 9 rows completed
2025-06-19 16:12:30,368 - INFO - Row 11: Processing Architecture/탑골공원
2025-06-19 16:12:30,368 - INFO - Row 11: Generating VQA without image
2025-06-19 16:12:41,952 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 16:12:41,963 - INFO - Raw API response type (text-only): <class 'str'>
2025-06-19 16:12:41,963 - INFO - Raw API response is None (text-only): False
2025-06-19 16:12:41,963 - INFO - Content length after strip (text-only): 0
2025-06-19 16:12:41,963 - INFO - Raw API response (text-only): ''...
2025-06-19 16:12:41,963 - INFO - FULL API response (text-only): ''
2025-06-19 16:12:41,963 - INFO - Cleaned content for JSON parsing (text-only): ''...
2025-06-19 16:12:41,963 - ERROR - Content is empty after cleaning (text-only)
2025-06-19 16:12:41,964 - ERROR - Row 11: Failed to generate VQA
2025-06-19 16:12:41,965 - INFO - Progress saved: 10 rows completed
2025-06-19 16:12:42,966 - INFO - Row 12: Processing Architecture/PC방
2025-06-19 16:12:42,967 - INFO - Accepting image URL: https://www.notion.so/image/attachment%3A48e41ea6-28e3-4cec-b7d0-87ce14fd90a0%3AIMG_1335.jpeg?table=...
2025-06-19 16:12:42,967 - INFO - Row 12: Generating VQA with image
2025-06-19 16:12:42,967 - INFO - Downloading image from URL: https://www.notion.so/image/attachment%3A48e41ea6-28e3-4cec-b7d0-87ce14fd90a0%3AIMG_1335.jpeg?table=block&id=1fc21dda-bbe5-81a6-ac0a-f30716bd2246&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:12:43,351 - ERROR - Failed to download/encode image from https://www.notion.so/image/attachment%3A48e41ea6-28e3-4cec-b7d0-87ce14fd90a0%3AIMG_1335.jpeg?table=block&id=1fc21dda-bbe5-81a6-ac0a-f30716bd2246&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2: 401 Client Error: Unauthorized for url: https://www.notion.so/image/attachment%3A48e41ea6-28e3-4cec-b7d0-87ce14fd90a0%3AIMG_1335.jpeg?table=block&id=1fc21dda-bbe5-81a6-ac0a-f30716bd2246&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:12:43,352 - ERROR - Failed to process image from https://www.notion.so/image/attachment%3A48e41ea6-28e3-4cec-b7d0-87ce14fd90a0%3AIMG_1335.jpeg?table=block&id=1fc21dda-bbe5-81a6-ac0a-f30716bd2246&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:12:43,352 - ERROR - Row 12: Failed to generate VQA
2025-06-19 16:12:43,353 - INFO - Progress saved: 11 rows completed
2025-06-19 16:12:44,354 - INFO - Row 13: Processing Architecture/분식집
2025-06-19 16:12:44,354 - INFO - Accepting image URL: https://www.google.com/url?sa=i&url=https%3A%2F%2Fcommons.wikimedia.org%2Fwiki%2FFile%3A2020-03-11_1...
2025-06-19 16:12:44,354 - INFO - Row 13: Generating VQA with image
2025-06-19 16:12:44,354 - INFO - Downloading image from URL: https://www.google.com/url?sa=i&url=https%3A%2F%2Fcommons.wikimedia.org%2Fwiki%2FFile%3A2020-03-11_12.23.44_%25EB%25B6%2584%25EC%258B%259D%25EC%25A7%2591.jpg&psig=AOvVaw2N_hZVLVNCU01ca_oOVZv9&ust=1750399946076000&source=images&cd=vfe&opi=89978449&ved=0CBQQjRxqFwoTCJDTtonq_I0DFQAAAAAdAAAAABAI
2025-06-19 16:12:45,039 - ERROR - Failed to download/encode image from https://www.google.com/url?sa=i&url=https%3A%2F%2Fcommons.wikimedia.org%2Fwiki%2FFile%3A2020-03-11_12.23.44_%25EB%25B6%2584%25EC%258B%259D%25EC%25A7%2591.jpg&psig=AOvVaw2N_hZVLVNCU01ca_oOVZv9&ust=1750399946076000&source=images&cd=vfe&opi=89978449&ved=0CBQQjRxqFwoTCJDTtonq_I0DFQAAAAAdAAAAABAI: cannot identify image file <_io.BytesIO object at 0x7c13bf37f9c0>
2025-06-19 16:12:45,039 - ERROR - Failed to process image from https://www.google.com/url?sa=i&url=https%3A%2F%2Fcommons.wikimedia.org%2Fwiki%2FFile%3A2020-03-11_12.23.44_%25EB%25B6%2584%25EC%258B%259D%25EC%25A7%2591.jpg&psig=AOvVaw2N_hZVLVNCU01ca_oOVZv9&ust=1750399946076000&source=images&cd=vfe&opi=89978449&ved=0CBQQjRxqFwoTCJDTtonq_I0DFQAAAAAdAAAAABAI
2025-06-19 16:12:45,040 - ERROR - Row 13: Failed to generate VQA
2025-06-19 16:12:45,040 - INFO - Progress saved: 12 rows completed
2025-06-19 16:12:46,041 - INFO - Row 14: Processing Architecture/빵집
2025-06-19 16:12:46,042 - INFO - Accepting image URL: https://www.notion.so/image/attachment%3A82336577-815a-4e2c-8775-e01ee172632d%3AIMG_0368.jpeg?table=...
2025-06-19 16:12:46,042 - INFO - Row 14: Generating VQA with image
2025-06-19 16:12:46,042 - INFO - Downloading image from URL: https://www.notion.so/image/attachment%3A82336577-815a-4e2c-8775-e01ee172632d%3AIMG_0368.jpeg?table=block&id=1fc21dda-bbe5-817b-95de-d0df3cdf18c1&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:12:46,473 - ERROR - Failed to download/encode image from https://www.notion.so/image/attachment%3A82336577-815a-4e2c-8775-e01ee172632d%3AIMG_0368.jpeg?table=block&id=1fc21dda-bbe5-817b-95de-d0df3cdf18c1&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2: 401 Client Error: Unauthorized for url: https://www.notion.so/image/attachment%3A82336577-815a-4e2c-8775-e01ee172632d%3AIMG_0368.jpeg?table=block&id=1fc21dda-bbe5-817b-95de-d0df3cdf18c1&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:12:46,473 - ERROR - Failed to process image from https://www.notion.so/image/attachment%3A82336577-815a-4e2c-8775-e01ee172632d%3AIMG_0368.jpeg?table=block&id=1fc21dda-bbe5-817b-95de-d0df3cdf18c1&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:12:46,473 - ERROR - Row 14: Failed to generate VQA
2025-06-19 16:12:46,473 - INFO - Progress saved: 13 rows completed
2025-06-19 16:12:47,474 - INFO - Row 15: Processing Architecture/광화문
2025-06-19 16:12:47,475 - INFO - Accepting image URL: https://images.unsplash.com/photo-1615428277562-f2dd4b887de2?q=80&w=2670&auto=format&fit=crop&ixlib=...
2025-06-19 16:12:47,475 - INFO - Row 15: Generating VQA with image
2025-06-19 16:12:47,475 - INFO - Downloading image from URL: https://images.unsplash.com/photo-1615428277562-f2dd4b887de2?q=80&w=2670&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D
2025-06-19 16:12:47,694 - INFO - Resized image to (2048, 1365)
2025-06-19 16:12:47,704 - INFO - Successfully processed image, size: 791122 bytes
2025-06-19 16:13:01,110 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 16:13:01,111 - INFO - Raw API response type: <class 'str'>
2025-06-19 16:13:01,111 - INFO - Raw API response is None: False
2025-06-19 16:13:01,111 - INFO - Content length after strip: 0
2025-06-19 16:13:01,111 - INFO - Raw API response: ''...
2025-06-19 16:13:01,111 - INFO - FULL API response: ''
2025-06-19 16:13:01,111 - INFO - Cleaned content for JSON parsing: ''...
2025-06-19 16:13:01,111 - ERROR - Content is empty after cleaning
2025-06-19 16:13:01,111 - ERROR - Row 15: Failed to generate VQA
2025-06-19 16:13:01,111 - INFO - Progress saved: 14 rows completed
2025-06-19 16:13:02,113 - INFO - Row 16: Processing Architecture/대형마트
2025-06-19 16:13:02,113 - INFO - Accepting image URL: https://www.notion.so/image/attachment%3A2718f1fe-e40e-4a61-9199-27a2db2cd35b%3AIMG_7274.jpeg?table=...
2025-06-19 16:13:02,113 - INFO - Row 16: Generating VQA with image
2025-06-19 16:13:02,113 - INFO - Downloading image from URL: https://www.notion.so/image/attachment%3A2718f1fe-e40e-4a61-9199-27a2db2cd35b%3AIMG_7274.jpeg?table=block&id=1fc21dda-bbe5-81c7-acf3-cffa7ce54fe5&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:13:02,399 - ERROR - Failed to download/encode image from https://www.notion.so/image/attachment%3A2718f1fe-e40e-4a61-9199-27a2db2cd35b%3AIMG_7274.jpeg?table=block&id=1fc21dda-bbe5-81c7-acf3-cffa7ce54fe5&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2: 401 Client Error: Unauthorized for url: https://www.notion.so/image/attachment%3A2718f1fe-e40e-4a61-9199-27a2db2cd35b%3AIMG_7274.jpeg?table=block&id=1fc21dda-bbe5-81c7-acf3-cffa7ce54fe5&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:13:02,400 - ERROR - Failed to process image from https://www.notion.so/image/attachment%3A2718f1fe-e40e-4a61-9199-27a2db2cd35b%3AIMG_7274.jpeg?table=block&id=1fc21dda-bbe5-81c7-acf3-cffa7ce54fe5&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:13:02,400 - ERROR - Row 16: Failed to generate VQA
2025-06-19 16:13:02,400 - INFO - Progress saved: 15 rows completed
2025-06-19 16:13:03,401 - INFO - Row 17: Processing Architecture/떡집
2025-06-19 16:13:03,402 - INFO - Accepting image URL: https://www.shutterstock.com/shutterstock/photos/709396000/display_1500/stock-photo-chung-cake-on-al...
2025-06-19 16:13:03,402 - INFO - Row 17: Generating VQA with image
2025-06-19 16:13:03,402 - INFO - Downloading image from URL: https://www.shutterstock.com/shutterstock/photos/709396000/display_1500/stock-photo-chung-cake-on-altar-in-old-village-communal-house-cooked-square-glutinous-rice-cake-vietnamese-709396000.jpg
2025-06-19 16:13:03,984 - INFO - Successfully processed image, size: 273396 bytes
2025-06-19 16:13:13,049 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 16:13:13,050 - INFO - Raw API response type: <class 'str'>
2025-06-19 16:13:13,051 - INFO - Raw API response is None: False
2025-06-19 16:13:13,051 - INFO - Content length after strip: 524
2025-06-19 16:13:13,051 - INFO - Raw API response: '{\n    "question": "This image shows ceremonial rice cakes presented on a low, open wooden platform inside a shrine. In designing a traditional Korean 떡집 (rice-cake shop), which architectural element s'...
2025-06-19 16:13:13,051 - INFO - FULL API response: '{\n    "question": "This image shows ceremonial rice cakes presented on a low, open wooden platform inside a shrine. In designing a traditional Korean 떡집 (rice-cake shop), which architectural element serves a similar function for displaying goods and creating an open, breathable space?",\n    "option_1": "마루 (Maru – raised wooden floor)",\n    "option_2": "온돌 (Ondol – underfloor heating)",\n    "option_3": "처마 (Cheoma – extended eaves)",\n    "option_4": "창호지 (Changhoji – paper sliding windows)",\n    "correct_option": "A"\n}'
2025-06-19 16:13:13,051 - INFO - Cleaned content for JSON parsing: '{\n    "question": "This image shows ceremonial rice cakes presented on a low, open wooden platform inside a shrine. In designing a traditional Korean 떡집 (rice-cake shop), which architectural element s'...
2025-06-19 16:13:13,051 - INFO - Row 17: Successfully generated VQA
2025-06-19 16:13:13,052 - INFO - Progress saved: 16 rows completed
2025-06-19 16:13:14,053 - INFO - Row 18: Processing Architecture/고기집
2025-06-19 16:13:14,053 - INFO - Accepting image URL: https://www.notion.so/image/attachment%3A5ac2cb64-d2e8-445a-9ca2-c4bf2dcf4406%3AIMG_0277.jpeg?table=...
2025-06-19 16:13:14,053 - INFO - Row 18: Generating VQA with image
2025-06-19 16:13:14,053 - INFO - Downloading image from URL: https://www.notion.so/image/attachment%3A5ac2cb64-d2e8-445a-9ca2-c4bf2dcf4406%3AIMG_0277.jpeg?table=block&id=1fc21dda-bbe5-8172-9e57-db1855275ff9&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:13:14,269 - ERROR - Failed to download/encode image from https://www.notion.so/image/attachment%3A5ac2cb64-d2e8-445a-9ca2-c4bf2dcf4406%3AIMG_0277.jpeg?table=block&id=1fc21dda-bbe5-8172-9e57-db1855275ff9&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2: 401 Client Error: Unauthorized for url: https://www.notion.so/image/attachment%3A5ac2cb64-d2e8-445a-9ca2-c4bf2dcf4406%3AIMG_0277.jpeg?table=block&id=1fc21dda-bbe5-8172-9e57-db1855275ff9&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:13:14,270 - ERROR - Failed to process image from https://www.notion.so/image/attachment%3A5ac2cb64-d2e8-445a-9ca2-c4bf2dcf4406%3AIMG_0277.jpeg?table=block&id=1fc21dda-bbe5-8172-9e57-db1855275ff9&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:13:14,270 - ERROR - Row 18: Failed to generate VQA
2025-06-19 16:13:14,271 - INFO - Progress saved: 17 rows completed
2025-06-19 16:13:15,272 - INFO - Row 19: Processing Architecture/찌개집
2025-06-19 16:13:15,272 - INFO - Row 19: Generating VQA without image
2025-06-19 16:13:23,232 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 16:13:23,234 - INFO - Raw API response type (text-only): <class 'str'>
2025-06-19 16:13:23,234 - INFO - Raw API response is None (text-only): False
2025-06-19 16:13:23,234 - INFO - Content length after strip (text-only): 0
2025-06-19 16:13:23,234 - INFO - Raw API response (text-only): ''...
2025-06-19 16:13:23,234 - INFO - FULL API response (text-only): ''
2025-06-19 16:13:23,234 - INFO - Cleaned content for JSON parsing (text-only): ''...
2025-06-19 16:13:23,234 - ERROR - Content is empty after cleaning (text-only)
2025-06-19 16:13:23,234 - ERROR - Row 19: Failed to generate VQA
2025-06-19 16:13:23,235 - INFO - Progress saved: 18 rows completed
2025-06-19 16:13:24,236 - INFO - Row 20: Processing Architecture/국밥집
2025-06-19 16:13:24,236 - INFO - Row 20: Generating VQA without image
2025-06-19 16:13:35,133 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 16:13:35,135 - INFO - Raw API response type (text-only): <class 'str'>
2025-06-19 16:13:35,135 - INFO - Raw API response is None (text-only): False
2025-06-19 16:13:35,135 - INFO - Content length after strip (text-only): 0
2025-06-19 16:13:35,135 - INFO - Raw API response (text-only): ''...
2025-06-19 16:13:35,135 - INFO - FULL API response (text-only): ''
2025-06-19 16:13:35,135 - INFO - Cleaned content for JSON parsing (text-only): ''...
2025-06-19 16:13:35,135 - ERROR - Content is empty after cleaning (text-only)
2025-06-19 16:13:35,135 - ERROR - Row 20: Failed to generate VQA
2025-06-19 16:13:35,136 - INFO - Progress saved: 19 rows completed
2025-06-19 16:13:36,137 - INFO - Row 21: Processing Architecture/해동용궁사
2025-06-19 16:13:36,137 - INFO - Accepting image URL: https://www.notion.so/image/attachment%3Ad145d5f2-4505-4cf0-b02e-cd7b9eb75b18%3AIMG_1855.jpeg?table=...
2025-06-19 16:13:36,137 - INFO - Row 21: Generating VQA with image
2025-06-19 16:13:36,137 - INFO - Downloading image from URL: https://www.notion.so/image/attachment%3Ad145d5f2-4505-4cf0-b02e-cd7b9eb75b18%3AIMG_1855.jpeg?table=block&id=1fc21dda-bbe5-8116-819c-d25bafc7cae6&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:13:36,789 - ERROR - Failed to download/encode image from https://www.notion.so/image/attachment%3Ad145d5f2-4505-4cf0-b02e-cd7b9eb75b18%3AIMG_1855.jpeg?table=block&id=1fc21dda-bbe5-8116-819c-d25bafc7cae6&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2: 401 Client Error: Unauthorized for url: https://www.notion.so/image/attachment%3Ad145d5f2-4505-4cf0-b02e-cd7b9eb75b18%3AIMG_1855.jpeg?table=block&id=1fc21dda-bbe5-8116-819c-d25bafc7cae6&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:13:36,790 - ERROR - Failed to process image from https://www.notion.so/image/attachment%3Ad145d5f2-4505-4cf0-b02e-cd7b9eb75b18%3AIMG_1855.jpeg?table=block&id=1fc21dda-bbe5-8116-819c-d25bafc7cae6&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:13:36,790 - ERROR - Row 21: Failed to generate VQA
2025-06-19 16:13:36,791 - INFO - Progress saved: 20 rows completed
2025-06-19 16:13:37,792 - INFO - Row 22: Processing Architecture/재래시장
2025-06-19 16:13:37,792 - INFO - Row 22: Generating VQA without image
2025-06-19 16:13:46,480 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 16:13:46,482 - INFO - Raw API response type (text-only): <class 'str'>
2025-06-19 16:13:46,482 - INFO - Raw API response is None (text-only): False
2025-06-19 16:13:46,482 - INFO - Content length after strip (text-only): 0
2025-06-19 16:13:46,482 - INFO - Raw API response (text-only): ''...
2025-06-19 16:13:46,482 - INFO - FULL API response (text-only): ''
2025-06-19 16:13:46,482 - INFO - Cleaned content for JSON parsing (text-only): ''...
2025-06-19 16:13:46,482 - ERROR - Content is empty after cleaning (text-only)
2025-06-19 16:13:46,483 - ERROR - Row 22: Failed to generate VQA
2025-06-19 16:13:46,484 - INFO - Progress saved: 21 rows completed
2025-06-19 16:13:47,485 - INFO - Row 23: Processing Architecture/한옥마을
2025-06-19 16:13:47,486 - INFO - Accepting image URL: https://www.notion.so/image/attachment%3A594464f8-ed34-426f-8dbc-db537dcd0f1d%3AIMG_5105.jpeg?table=...
2025-06-19 16:13:47,486 - INFO - Row 23: Generating VQA with image
2025-06-19 16:13:47,486 - INFO - Downloading image from URL: https://www.notion.so/image/attachment%3A594464f8-ed34-426f-8dbc-db537dcd0f1d%3AIMG_5105.jpeg?table=block&id=1fc21dda-bbe5-8108-b23e-cd6eedb005bf&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:13:47,540 - INFO - Generation interrupted by user
2025-06-19 16:15:16,385 - INFO - Initializing VQA Generator...
2025-06-19 16:15:16,403 - INFO - Loaded generation rules from /mnt/raid6/junkim100/east-asia/VQA_Generation_Rules.md
2025-06-19 16:15:16,403 - INFO - Processing CSV file...
2025-06-19 16:15:16,403 - INFO - Progress will be saved to: VQA _vqa_progress.csv
2025-06-19 16:15:16,403 - INFO - Row 2: Processing Architecture/제주 돌집
2025-06-19 16:15:16,403 - INFO - Accepting image URL: https://www.notion.so/image/attachment%3A619b2f68-f70e-435a-b659-b51be324c20a%3AScreenshot_2025-05-2...
2025-06-19 16:15:16,403 - INFO - Row 2: Generating VQA with image
2025-06-19 16:15:16,403 - INFO - Downloading image from URL: https://www.notion.so/image/attachment%3A619b2f68-f70e-435a-b659-b51be324c20a%3AScreenshot_2025-05-23_at_2.47.10_PM.png?table=block&id=1fc21dda-bbe5-8146-904f-f3d7e20dda3f&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:15:16,690 - ERROR - Failed to download/encode image from https://www.notion.so/image/attachment%3A619b2f68-f70e-435a-b659-b51be324c20a%3AScreenshot_2025-05-23_at_2.47.10_PM.png?table=block&id=1fc21dda-bbe5-8146-904f-f3d7e20dda3f&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2: 401 Client Error: Unauthorized for url: https://www.notion.so/image/attachment%3A619b2f68-f70e-435a-b659-b51be324c20a%3AScreenshot_2025-05-23_at_2.47.10_PM.png?table=block&id=1fc21dda-bbe5-8146-904f-f3d7e20dda3f&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:15:16,691 - ERROR - Failed to process image from https://www.notion.so/image/attachment%3A619b2f68-f70e-435a-b659-b51be324c20a%3AScreenshot_2025-05-23_at_2.47.10_PM.png?table=block&id=1fc21dda-bbe5-8146-904f-f3d7e20dda3f&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:15:16,691 - ERROR - Row 2: Failed to generate VQA
2025-06-19 16:15:16,692 - INFO - Progress saved: 1 rows completed
2025-06-19 16:15:17,693 - INFO - Row 3: Processing Architecture/월정교
2025-06-19 16:15:17,694 - INFO - Accepting image URL: https://www.notion.so/image/attachment%3A52ff9a7c-cf16-4f4a-baee-a84a4592cfff%3AIMG_0656.jpeg?table=...
2025-06-19 16:15:17,694 - INFO - Row 3: Generating VQA with image
2025-06-19 16:15:17,694 - INFO - Downloading image from URL: https://www.notion.so/image/attachment%3A52ff9a7c-cf16-4f4a-baee-a84a4592cfff%3AIMG_0656.jpeg?table=block&id=1fc21dda-bbe5-815c-8c4f-fbdd92a718eb&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:15:17,884 - ERROR - Failed to download/encode image from https://www.notion.so/image/attachment%3A52ff9a7c-cf16-4f4a-baee-a84a4592cfff%3AIMG_0656.jpeg?table=block&id=1fc21dda-bbe5-815c-8c4f-fbdd92a718eb&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2: 401 Client Error: Unauthorized for url: https://www.notion.so/image/attachment%3A52ff9a7c-cf16-4f4a-baee-a84a4592cfff%3AIMG_0656.jpeg?table=block&id=1fc21dda-bbe5-815c-8c4f-fbdd92a718eb&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:15:17,884 - ERROR - Failed to process image from https://www.notion.so/image/attachment%3A52ff9a7c-cf16-4f4a-baee-a84a4592cfff%3AIMG_0656.jpeg?table=block&id=1fc21dda-bbe5-815c-8c4f-fbdd92a718eb&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:15:17,884 - ERROR - Row 3: Failed to generate VQA
2025-06-19 16:15:17,885 - INFO - Progress saved: 2 rows completed
2025-06-19 16:15:18,886 - INFO - Row 4: Processing Architecture/운현궁
2025-06-19 16:15:18,887 - INFO - Accepting image URL: https://www.notion.so/image/attachment%3Ae997452e-ac86-4583-9980-de73b2c475ca%3AIMG_1269.jpeg?table=...
2025-06-19 16:15:18,887 - INFO - Row 4: Generating VQA with image
2025-06-19 16:15:18,887 - INFO - Downloading image from URL: https://www.notion.so/image/attachment%3Ae997452e-ac86-4583-9980-de73b2c475ca%3AIMG_1269.jpeg?table=block&id=1fc21dda-bbe5-813b-afc9-fd73a8b1fe3b&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:15:19,295 - ERROR - Failed to download/encode image from https://www.notion.so/image/attachment%3Ae997452e-ac86-4583-9980-de73b2c475ca%3AIMG_1269.jpeg?table=block&id=1fc21dda-bbe5-813b-afc9-fd73a8b1fe3b&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2: 401 Client Error: Unauthorized for url: https://www.notion.so/image/attachment%3Ae997452e-ac86-4583-9980-de73b2c475ca%3AIMG_1269.jpeg?table=block&id=1fc21dda-bbe5-813b-afc9-fd73a8b1fe3b&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:15:19,296 - ERROR - Failed to process image from https://www.notion.so/image/attachment%3Ae997452e-ac86-4583-9980-de73b2c475ca%3AIMG_1269.jpeg?table=block&id=1fc21dda-bbe5-813b-afc9-fd73a8b1fe3b&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:15:19,296 - ERROR - Row 4: Failed to generate VQA
2025-06-19 16:15:19,297 - INFO - Progress saved: 3 rows completed
2025-06-19 16:15:20,298 - INFO - Row 5: Processing Architecture/명동
2025-06-19 16:15:20,298 - INFO - Accepting image URL: https://images.unsplash.com/photo-1677097610167-c2d62b06fad3?q=80&w=2670&auto=format&fit=crop&ixlib=...
2025-06-19 16:15:20,298 - INFO - Row 5: Generating VQA with image
2025-06-19 16:15:20,298 - INFO - Downloading image from URL: https://images.unsplash.com/photo-1677097610167-c2d62b06fad3?q=80&w=2670&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D
2025-06-19 16:15:20,570 - INFO - Resized image to (2048, 1365)
2025-06-19 16:15:20,581 - INFO - Successfully processed image, size: 651189 bytes
2025-06-19 16:15:30,536 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 16:15:30,542 - INFO - Raw API response type: <class 'str'>
2025-06-19 16:15:30,542 - INFO - Raw API response is None: False
2025-06-19 16:15:30,542 - INFO - Content length after strip: 0
2025-06-19 16:15:30,543 - INFO - Raw API response: ''...
2025-06-19 16:15:30,543 - INFO - FULL API response: ''
2025-06-19 16:15:30,543 - INFO - Cleaned content for JSON parsing: ''...
2025-06-19 16:15:30,543 - ERROR - Content is empty after cleaning - possibly content filtered
2025-06-19 16:15:30,543 - INFO - Retrying image generation (attempt 1)
2025-06-19 16:15:32,545 - INFO - Downloading image from URL: https://images.unsplash.com/photo-1677097610167-c2d62b06fad3?q=80&w=2670&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D
2025-06-19 16:15:33,472 - INFO - Resized image to (2048, 1365)
2025-06-19 16:15:33,482 - INFO - Successfully processed image, size: 651189 bytes
2025-06-19 16:15:45,576 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 16:15:45,577 - INFO - Raw API response type: <class 'str'>
2025-06-19 16:15:45,577 - INFO - Raw API response is None: False
2025-06-19 16:15:45,577 - INFO - Content length after strip: 0
2025-06-19 16:15:45,577 - INFO - Raw API response: ''...
2025-06-19 16:15:45,577 - INFO - FULL API response: ''
2025-06-19 16:15:45,577 - INFO - Cleaned content for JSON parsing: ''...
2025-06-19 16:15:45,577 - ERROR - Content is empty after cleaning - possibly content filtered
2025-06-19 16:15:45,577 - INFO - Falling back to text-only generation for this item
2025-06-19 16:15:55,554 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 16:15:55,558 - INFO - Raw API response type (text-only): <class 'str'>
2025-06-19 16:15:55,558 - INFO - Raw API response is None (text-only): False
2025-06-19 16:15:55,558 - INFO - Content length after strip (text-only): 0
2025-06-19 16:15:55,559 - INFO - Raw API response (text-only): ''...
2025-06-19 16:15:55,559 - INFO - FULL API response (text-only): ''
2025-06-19 16:15:55,559 - INFO - Cleaned content for JSON parsing (text-only): ''...
2025-06-19 16:15:55,559 - ERROR - Content is empty after cleaning (text-only)
2025-06-19 16:15:55,559 - ERROR - Row 5: Failed to generate VQA
2025-06-19 16:15:55,559 - INFO - Progress saved: 4 rows completed
2025-06-19 16:15:56,561 - INFO - Row 6: Processing Architecture/남산타워
2025-06-19 16:15:56,561 - INFO - Accepting image URL: https://www.notion.so/image/attachment%3A1047482e-93e8-4f25-9590-52f3dfd1baa8%3AIMG_1727.jpeg?table=...
2025-06-19 16:15:56,561 - INFO - Row 6: Generating VQA with image
2025-06-19 16:15:56,561 - INFO - Downloading image from URL: https://www.notion.so/image/attachment%3A1047482e-93e8-4f25-9590-52f3dfd1baa8%3AIMG_1727.jpeg?table=block&id=1fc21dda-bbe5-81d2-a305-e446ddc791f2&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:15:56,863 - ERROR - Failed to download/encode image from https://www.notion.so/image/attachment%3A1047482e-93e8-4f25-9590-52f3dfd1baa8%3AIMG_1727.jpeg?table=block&id=1fc21dda-bbe5-81d2-a305-e446ddc791f2&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2: 401 Client Error: Unauthorized for url: https://www.notion.so/image/attachment%3A1047482e-93e8-4f25-9590-52f3dfd1baa8%3AIMG_1727.jpeg?table=block&id=1fc21dda-bbe5-81d2-a305-e446ddc791f2&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:15:56,863 - ERROR - Failed to process image from https://www.notion.so/image/attachment%3A1047482e-93e8-4f25-9590-52f3dfd1baa8%3AIMG_1727.jpeg?table=block&id=1fc21dda-bbe5-81d2-a305-e446ddc791f2&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:15:56,863 - ERROR - Row 6: Failed to generate VQA
2025-06-19 16:15:56,864 - INFO - Progress saved: 5 rows completed
2025-06-19 16:15:57,865 - INFO - Row 7: Processing Architecture/신라대종
2025-06-19 16:15:57,866 - INFO - Accepting image URL: https://www.notion.so/image/attachment%3A18f7bd18-ce33-488c-9e30-e7f153449bb9%3AIMG_0587.jpeg?table=...
2025-06-19 16:15:57,866 - INFO - Row 7: Generating VQA with image
2025-06-19 16:15:57,866 - INFO - Downloading image from URL: https://www.notion.so/image/attachment%3A18f7bd18-ce33-488c-9e30-e7f153449bb9%3AIMG_0587.jpeg?table=block&id=1fc21dda-bbe5-81ee-b974-f4ef3de73ceb&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:15:58,240 - ERROR - Failed to download/encode image from https://www.notion.so/image/attachment%3A18f7bd18-ce33-488c-9e30-e7f153449bb9%3AIMG_0587.jpeg?table=block&id=1fc21dda-bbe5-81ee-b974-f4ef3de73ceb&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2: 401 Client Error: Unauthorized for url: https://www.notion.so/image/attachment%3A18f7bd18-ce33-488c-9e30-e7f153449bb9%3AIMG_0587.jpeg?table=block&id=1fc21dda-bbe5-81ee-b974-f4ef3de73ceb&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:15:58,241 - ERROR - Failed to process image from https://www.notion.so/image/attachment%3A18f7bd18-ce33-488c-9e30-e7f153449bb9%3AIMG_0587.jpeg?table=block&id=1fc21dda-bbe5-81ee-b974-f4ef3de73ceb&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:15:58,241 - ERROR - Row 7: Failed to generate VQA
2025-06-19 16:15:58,241 - INFO - Progress saved: 6 rows completed
2025-06-19 16:15:59,242 - INFO - Row 8: Processing Architecture/고려대학교
2025-06-19 16:15:59,243 - INFO - Accepting image URL: https://www.notion.so/image/attachment%3Aadaf8ea1-e599-40a0-89d5-c292ee2780fd%3AIMG_3619.jpeg?table=...
2025-06-19 16:15:59,243 - INFO - Row 8: Generating VQA with image
2025-06-19 16:15:59,243 - INFO - Downloading image from URL: https://www.notion.so/image/attachment%3Aadaf8ea1-e599-40a0-89d5-c292ee2780fd%3AIMG_3619.jpeg?table=block&id=1fc21dda-bbe5-81fe-98d5-e8850a9f2ba7&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:15:59,438 - ERROR - Failed to download/encode image from https://www.notion.so/image/attachment%3Aadaf8ea1-e599-40a0-89d5-c292ee2780fd%3AIMG_3619.jpeg?table=block&id=1fc21dda-bbe5-81fe-98d5-e8850a9f2ba7&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2: 401 Client Error: Unauthorized for url: https://www.notion.so/image/attachment%3Aadaf8ea1-e599-40a0-89d5-c292ee2780fd%3AIMG_3619.jpeg?table=block&id=1fc21dda-bbe5-81fe-98d5-e8850a9f2ba7&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:15:59,439 - ERROR - Failed to process image from https://www.notion.so/image/attachment%3Aadaf8ea1-e599-40a0-89d5-c292ee2780fd%3AIMG_3619.jpeg?table=block&id=1fc21dda-bbe5-81fe-98d5-e8850a9f2ba7&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:15:59,439 - ERROR - Row 8: Failed to generate VQA
2025-06-19 16:15:59,439 - INFO - Progress saved: 7 rows completed
2025-06-19 16:16:00,441 - INFO - Row 9: Processing Architecture/한강다리
2025-06-19 16:16:00,441 - INFO - Accepting image URL: https://plus.unsplash.com/premium_photo-1716968594404-ac5ae8cdcdc4?q=80&w=2667&auto=format&fit=crop&...
2025-06-19 16:16:00,441 - INFO - Row 9: Generating VQA with image
2025-06-19 16:16:00,441 - INFO - Downloading image from URL: https://plus.unsplash.com/premium_photo-1716968594404-ac5ae8cdcdc4?q=80&w=2667&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D
2025-06-19 16:16:01,855 - INFO - Resized image to (2048, 1370)
2025-06-19 16:16:01,863 - INFO - Successfully processed image, size: 342656 bytes
2025-06-19 16:16:14,188 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 16:16:14,189 - INFO - Raw API response type: <class 'str'>
2025-06-19 16:16:14,189 - INFO - Raw API response is None: False
2025-06-19 16:16:14,189 - INFO - Content length after strip: 259
2025-06-19 16:16:14,189 - INFO - Raw API response: '{\n    "question": "이 사진 속 한강 위 다리 중 최초로 건설되어 한국 근대화의 상징으로 여겨진 다리는 어느 시기에 완공되었는가?",\n    "option_1": "조선 말기 (1890년대)",\n    "option_2": "일제강점기 (1910~1945년)",\n    "option_3": "한국전쟁 이후 (1950~1960년대)",\n    '...
2025-06-19 16:16:14,189 - INFO - FULL API response: '{\n    "question": "이 사진 속 한강 위 다리 중 최초로 건설되어 한국 근대화의 상징으로 여겨진 다리는 어느 시기에 완공되었는가?",\n    "option_1": "조선 말기 (1890년대)",\n    "option_2": "일제강점기 (1910~1945년)",\n    "option_3": "한국전쟁 이후 (1950~1960년대)",\n    "option_4": "21세기 (2000년대 이후)",\n    "correct_option": "B"\n}'
2025-06-19 16:16:14,189 - INFO - Cleaned content for JSON parsing: '{\n    "question": "이 사진 속 한강 위 다리 중 최초로 건설되어 한국 근대화의 상징으로 여겨진 다리는 어느 시기에 완공되었는가?",\n    "option_1": "조선 말기 (1890년대)",\n    "option_2": "일제강점기 (1910~1945년)",\n    "option_3": "한국전쟁 이후 (1950~1960년대)",\n    '...
2025-06-19 16:16:14,189 - INFO - Row 9: Successfully generated VQA
2025-06-19 16:16:14,190 - INFO - Progress saved: 8 rows completed
2025-06-19 16:16:15,191 - INFO - Row 10: Processing Architecture/DDP
2025-06-19 16:16:15,191 - INFO - Accepting image URL: https://www.notion.so/image/attachment%3Afae18d60-ca9f-4e84-aab6-d4e76c22b174%3AIMG_1324.jpeg?table=...
2025-06-19 16:16:15,191 - INFO - Row 10: Generating VQA with image
2025-06-19 16:16:15,191 - INFO - Downloading image from URL: https://www.notion.so/image/attachment%3Afae18d60-ca9f-4e84-aab6-d4e76c22b174%3AIMG_1324.jpeg?table=block&id=1fc21dda-bbe5-815c-98ad-e69190909ffe&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:16:15,950 - ERROR - Failed to download/encode image from https://www.notion.so/image/attachment%3Afae18d60-ca9f-4e84-aab6-d4e76c22b174%3AIMG_1324.jpeg?table=block&id=1fc21dda-bbe5-815c-98ad-e69190909ffe&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2: 401 Client Error: Unauthorized for url: https://www.notion.so/image/attachment%3Afae18d60-ca9f-4e84-aab6-d4e76c22b174%3AIMG_1324.jpeg?table=block&id=1fc21dda-bbe5-815c-98ad-e69190909ffe&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:16:15,950 - ERROR - Failed to process image from https://www.notion.so/image/attachment%3Afae18d60-ca9f-4e84-aab6-d4e76c22b174%3AIMG_1324.jpeg?table=block&id=1fc21dda-bbe5-815c-98ad-e69190909ffe&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:16:15,950 - ERROR - Row 10: Failed to generate VQA
2025-06-19 16:16:15,951 - INFO - Progress saved: 9 rows completed
2025-06-19 16:16:16,952 - INFO - Row 11: Processing Architecture/탑골공원
2025-06-19 16:16:16,953 - INFO - Row 11: Generating VQA without image
2025-06-19 16:16:25,919 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 16:16:25,921 - INFO - Raw API response type (text-only): <class 'str'>
2025-06-19 16:16:25,921 - INFO - Raw API response is None (text-only): False
2025-06-19 16:16:25,921 - INFO - Content length after strip (text-only): 267
2025-06-19 16:16:25,921 - INFO - Raw API response (text-only): '{"question":"탑골공원 내 원각사지 10층석탑(국보 제2호)의 돌기반 석탑에서 ‘목조건축 양식의 석조 모방’ 기법을 보여주는 대표적 건축적 특징은 무엇인가?","option_1":"각 층 지붕돌 밑면에 새긴 기둥모양 공포(지지구조)","option_2":"기단부 연화문을 중심으로 배치된 사천왕상 조각","option_3":"탑 상륜부의 일체형 원형'...
2025-06-19 16:16:25,921 - INFO - FULL API response (text-only): '{"question":"탑골공원 내 원각사지 10층석탑(국보 제2호)의 돌기반 석탑에서 ‘목조건축 양식의 석조 모방’ 기법을 보여주는 대표적 건축적 특징은 무엇인가?","option_1":"각 층 지붕돌 밑면에 새긴 기둥모양 공포(지지구조)","option_2":"기단부 연화문을 중심으로 배치된 사천왕상 조각","option_3":"탑 상륜부의 일체형 원형 보주 형식","option_4":"탑 전체에 새겨진 연꽃잎 무늬의 일관된 적용","correct_option":"A"}'
2025-06-19 16:16:25,921 - INFO - Cleaned content for JSON parsing (text-only): '{"question":"탑골공원 내 원각사지 10층석탑(국보 제2호)의 돌기반 석탑에서 ‘목조건축 양식의 석조 모방’ 기법을 보여주는 대표적 건축적 특징은 무엇인가?","option_1":"각 층 지붕돌 밑면에 새긴 기둥모양 공포(지지구조)","option_2":"기단부 연화문을 중심으로 배치된 사천왕상 조각","option_3":"탑 상륜부의 일체형 원형'...
2025-06-19 16:16:25,921 - INFO - Row 11: Successfully generated VQA
2025-06-19 16:16:25,922 - INFO - Progress saved: 10 rows completed
2025-06-19 16:16:26,923 - INFO - Row 12: Processing Architecture/PC방
2025-06-19 16:16:26,924 - INFO - Accepting image URL: https://www.notion.so/image/attachment%3A48e41ea6-28e3-4cec-b7d0-87ce14fd90a0%3AIMG_1335.jpeg?table=...
2025-06-19 16:16:26,924 - INFO - Row 12: Generating VQA with image
2025-06-19 16:16:26,924 - INFO - Downloading image from URL: https://www.notion.so/image/attachment%3A48e41ea6-28e3-4cec-b7d0-87ce14fd90a0%3AIMG_1335.jpeg?table=block&id=1fc21dda-bbe5-81a6-ac0a-f30716bd2246&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:16:27,238 - ERROR - Failed to download/encode image from https://www.notion.so/image/attachment%3A48e41ea6-28e3-4cec-b7d0-87ce14fd90a0%3AIMG_1335.jpeg?table=block&id=1fc21dda-bbe5-81a6-ac0a-f30716bd2246&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2: 401 Client Error: Unauthorized for url: https://www.notion.so/image/attachment%3A48e41ea6-28e3-4cec-b7d0-87ce14fd90a0%3AIMG_1335.jpeg?table=block&id=1fc21dda-bbe5-81a6-ac0a-f30716bd2246&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:16:27,238 - ERROR - Failed to process image from https://www.notion.so/image/attachment%3A48e41ea6-28e3-4cec-b7d0-87ce14fd90a0%3AIMG_1335.jpeg?table=block&id=1fc21dda-bbe5-81a6-ac0a-f30716bd2246&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:16:27,239 - ERROR - Row 12: Failed to generate VQA
2025-06-19 16:16:27,239 - INFO - Progress saved: 11 rows completed
2025-06-19 16:16:28,241 - INFO - Row 13: Processing Architecture/분식집
2025-06-19 16:16:28,241 - INFO - Accepting image URL: https://www.google.com/url?sa=i&url=https%3A%2F%2Fcommons.wikimedia.org%2Fwiki%2FFile%3A2020-03-11_1...
2025-06-19 16:16:28,241 - INFO - Row 13: Generating VQA with image
2025-06-19 16:16:28,241 - INFO - Downloading image from URL: https://www.google.com/url?sa=i&url=https%3A%2F%2Fcommons.wikimedia.org%2Fwiki%2FFile%3A2020-03-11_12.23.44_%25EB%25B6%2584%25EC%258B%259D%25EC%25A7%2591.jpg&psig=AOvVaw2N_hZVLVNCU01ca_oOVZv9&ust=1750399946076000&source=images&cd=vfe&opi=89978449&ved=0CBQQjRxqFwoTCJDTtonq_I0DFQAAAAAdAAAAABAI
2025-06-19 16:16:29,119 - ERROR - Failed to download/encode image from https://www.google.com/url?sa=i&url=https%3A%2F%2Fcommons.wikimedia.org%2Fwiki%2FFile%3A2020-03-11_12.23.44_%25EB%25B6%2584%25EC%258B%259D%25EC%25A7%2591.jpg&psig=AOvVaw2N_hZVLVNCU01ca_oOVZv9&ust=1750399946076000&source=images&cd=vfe&opi=89978449&ved=0CBQQjRxqFwoTCJDTtonq_I0DFQAAAAAdAAAAABAI: cannot identify image file <_io.BytesIO object at 0x79c8f868ebb0>
2025-06-19 16:16:29,120 - ERROR - Failed to process image from https://www.google.com/url?sa=i&url=https%3A%2F%2Fcommons.wikimedia.org%2Fwiki%2FFile%3A2020-03-11_12.23.44_%25EB%25B6%2584%25EC%258B%259D%25EC%25A7%2591.jpg&psig=AOvVaw2N_hZVLVNCU01ca_oOVZv9&ust=1750399946076000&source=images&cd=vfe&opi=89978449&ved=0CBQQjRxqFwoTCJDTtonq_I0DFQAAAAAdAAAAABAI
2025-06-19 16:16:29,120 - ERROR - Row 13: Failed to generate VQA
2025-06-19 16:16:29,121 - INFO - Progress saved: 12 rows completed
2025-06-19 16:16:30,122 - INFO - Row 14: Processing Architecture/빵집
2025-06-19 16:16:30,122 - INFO - Accepting image URL: https://www.notion.so/image/attachment%3A82336577-815a-4e2c-8775-e01ee172632d%3AIMG_0368.jpeg?table=...
2025-06-19 16:16:30,122 - INFO - Row 14: Generating VQA with image
2025-06-19 16:16:30,122 - INFO - Downloading image from URL: https://www.notion.so/image/attachment%3A82336577-815a-4e2c-8775-e01ee172632d%3AIMG_0368.jpeg?table=block&id=1fc21dda-bbe5-817b-95de-d0df3cdf18c1&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:16:30,333 - ERROR - Failed to download/encode image from https://www.notion.so/image/attachment%3A82336577-815a-4e2c-8775-e01ee172632d%3AIMG_0368.jpeg?table=block&id=1fc21dda-bbe5-817b-95de-d0df3cdf18c1&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2: 401 Client Error: Unauthorized for url: https://www.notion.so/image/attachment%3A82336577-815a-4e2c-8775-e01ee172632d%3AIMG_0368.jpeg?table=block&id=1fc21dda-bbe5-817b-95de-d0df3cdf18c1&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:16:30,334 - ERROR - Failed to process image from https://www.notion.so/image/attachment%3A82336577-815a-4e2c-8775-e01ee172632d%3AIMG_0368.jpeg?table=block&id=1fc21dda-bbe5-817b-95de-d0df3cdf18c1&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:16:30,334 - ERROR - Row 14: Failed to generate VQA
2025-06-19 16:16:30,335 - INFO - Progress saved: 13 rows completed
2025-06-19 16:16:31,336 - INFO - Row 15: Processing Architecture/광화문
2025-06-19 16:16:31,337 - INFO - Accepting image URL: https://images.unsplash.com/photo-1615428277562-f2dd4b887de2?q=80&w=2670&auto=format&fit=crop&ixlib=...
2025-06-19 16:16:31,337 - INFO - Row 15: Generating VQA with image
2025-06-19 16:16:31,337 - INFO - Downloading image from URL: https://images.unsplash.com/photo-1615428277562-f2dd4b887de2?q=80&w=2670&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D
2025-06-19 16:16:31,625 - INFO - Resized image to (2048, 1365)
2025-06-19 16:16:31,636 - INFO - Successfully processed image, size: 791122 bytes
2025-06-19 16:16:43,469 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 16:16:43,470 - INFO - Raw API response type: <class 'str'>
2025-06-19 16:16:43,470 - INFO - Raw API response is None: False
2025-06-19 16:16:43,470 - INFO - Content length after strip: 267
2025-06-19 16:16:43,471 - INFO - Raw API response: '{"question":"The calligraphy on the Gwanghwamun signboard, visible in the image, was painted by which monarch during the 19th century?","option_1":"Sejong the Great","option_2":"King Gojong","option_3'...
2025-06-19 16:16:43,471 - INFO - FULL API response: '{"question":"The calligraphy on the Gwanghwamun signboard, visible in the image, was painted by which monarch during the 19th century?","option_1":"Sejong the Great","option_2":"King Gojong","option_3":"King Jeongjo","option_4":"Emperor Sunjong","correct_option":"B"}'
2025-06-19 16:16:43,471 - INFO - Cleaned content for JSON parsing: '{"question":"The calligraphy on the Gwanghwamun signboard, visible in the image, was painted by which monarch during the 19th century?","option_1":"Sejong the Great","option_2":"King Gojong","option_3'...
2025-06-19 16:16:43,471 - INFO - Row 15: Successfully generated VQA
2025-06-19 16:16:43,472 - INFO - Progress saved: 14 rows completed
2025-06-19 16:16:44,473 - INFO - Row 16: Processing Architecture/대형마트
2025-06-19 16:16:44,473 - INFO - Accepting image URL: https://www.notion.so/image/attachment%3A2718f1fe-e40e-4a61-9199-27a2db2cd35b%3AIMG_7274.jpeg?table=...
2025-06-19 16:16:44,473 - INFO - Row 16: Generating VQA with image
2025-06-19 16:16:44,473 - INFO - Downloading image from URL: https://www.notion.so/image/attachment%3A2718f1fe-e40e-4a61-9199-27a2db2cd35b%3AIMG_7274.jpeg?table=block&id=1fc21dda-bbe5-81c7-acf3-cffa7ce54fe5&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:16:44,691 - ERROR - Failed to download/encode image from https://www.notion.so/image/attachment%3A2718f1fe-e40e-4a61-9199-27a2db2cd35b%3AIMG_7274.jpeg?table=block&id=1fc21dda-bbe5-81c7-acf3-cffa7ce54fe5&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2: 401 Client Error: Unauthorized for url: https://www.notion.so/image/attachment%3A2718f1fe-e40e-4a61-9199-27a2db2cd35b%3AIMG_7274.jpeg?table=block&id=1fc21dda-bbe5-81c7-acf3-cffa7ce54fe5&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:16:44,692 - ERROR - Failed to process image from https://www.notion.so/image/attachment%3A2718f1fe-e40e-4a61-9199-27a2db2cd35b%3AIMG_7274.jpeg?table=block&id=1fc21dda-bbe5-81c7-acf3-cffa7ce54fe5&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:16:44,692 - ERROR - Row 16: Failed to generate VQA
2025-06-19 16:16:44,693 - INFO - Progress saved: 15 rows completed
2025-06-19 16:16:45,694 - INFO - Row 17: Processing Architecture/떡집
2025-06-19 16:16:45,695 - INFO - Accepting image URL: https://www.shutterstock.com/shutterstock/photos/709396000/display_1500/stock-photo-chung-cake-on-al...
2025-06-19 16:16:45,695 - INFO - Row 17: Generating VQA with image
2025-06-19 16:16:45,695 - INFO - Downloading image from URL: https://www.shutterstock.com/shutterstock/photos/709396000/display_1500/stock-photo-chung-cake-on-altar-in-old-village-communal-house-cooked-square-glutinous-rice-cake-vietnamese-709396000.jpg
2025-06-19 16:16:46,006 - INFO - Successfully processed image, size: 273396 bytes
2025-06-19 16:16:55,180 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 16:16:55,181 - INFO - Raw API response type: <class 'str'>
2025-06-19 16:16:55,181 - INFO - Raw API response is None: False
2025-06-19 16:16:55,181 - INFO - Content length after strip: 0
2025-06-19 16:16:55,181 - INFO - Raw API response: ''...
2025-06-19 16:16:55,181 - INFO - FULL API response: ''
2025-06-19 16:16:55,181 - INFO - Cleaned content for JSON parsing: ''...
2025-06-19 16:16:55,181 - ERROR - Content is empty after cleaning - possibly content filtered
2025-06-19 16:16:55,182 - INFO - Retrying image generation (attempt 1)
2025-06-19 16:16:57,184 - INFO - Downloading image from URL: https://www.shutterstock.com/shutterstock/photos/709396000/display_1500/stock-photo-chung-cake-on-altar-in-old-village-communal-house-cooked-square-glutinous-rice-cake-vietnamese-709396000.jpg
2025-06-19 16:16:57,261 - INFO - Successfully processed image, size: 273396 bytes
2025-06-19 16:17:08,261 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 16:17:08,262 - INFO - Raw API response type: <class 'str'>
2025-06-19 16:17:08,262 - INFO - Raw API response is None: False
2025-06-19 16:17:08,262 - INFO - Content length after strip: 0
2025-06-19 16:17:08,262 - INFO - Raw API response: ''...
2025-06-19 16:17:08,262 - INFO - FULL API response: ''
2025-06-19 16:17:08,262 - INFO - Cleaned content for JSON parsing: ''...
2025-06-19 16:17:08,262 - ERROR - Content is empty after cleaning - possibly content filtered
2025-06-19 16:17:08,262 - INFO - Falling back to text-only generation for this item
2025-06-19 16:17:15,882 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 16:17:15,883 - INFO - Raw API response type (text-only): <class 'str'>
2025-06-19 16:17:15,883 - INFO - Raw API response is None (text-only): False
2025-06-19 16:17:15,884 - INFO - Content length after strip (text-only): 376
2025-06-19 16:17:15,884 - INFO - Raw API response (text-only): '{\n    "question": "In traditional Joseon-era 떡집 (rice-cake shops), which architectural feature—a raised wooden platform with open eaves—was specifically designed for displaying and preserving rice cak'...
2025-06-19 16:17:15,884 - INFO - FULL API response (text-only): '{\n    "question": "In traditional Joseon-era 떡집 (rice-cake shops), which architectural feature—a raised wooden platform with open eaves—was specifically designed for displaying and preserving rice cakes by promoting air circulation?",\n    "option_1": "Daecheongmaru",\n    "option_2": "Ondol",\n    "option_3": "Sarangbang",\n    "option_4": "Madang",\n    "correct_option": "A"\n}'
2025-06-19 16:17:15,884 - INFO - Cleaned content for JSON parsing (text-only): '{\n    "question": "In traditional Joseon-era 떡집 (rice-cake shops), which architectural feature—a raised wooden platform with open eaves—was specifically designed for displaying and preserving rice cak'...
2025-06-19 16:17:15,884 - INFO - Row 17: Successfully generated VQA
2025-06-19 16:17:15,885 - INFO - Progress saved: 16 rows completed
2025-06-19 16:17:16,886 - INFO - Row 18: Processing Architecture/고기집
2025-06-19 16:17:16,886 - INFO - Accepting image URL: https://www.notion.so/image/attachment%3A5ac2cb64-d2e8-445a-9ca2-c4bf2dcf4406%3AIMG_0277.jpeg?table=...
2025-06-19 16:17:16,886 - INFO - Row 18: Generating VQA with image
2025-06-19 16:17:16,886 - INFO - Downloading image from URL: https://www.notion.so/image/attachment%3A5ac2cb64-d2e8-445a-9ca2-c4bf2dcf4406%3AIMG_0277.jpeg?table=block&id=1fc21dda-bbe5-8172-9e57-db1855275ff9&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:17:17,324 - ERROR - Failed to download/encode image from https://www.notion.so/image/attachment%3A5ac2cb64-d2e8-445a-9ca2-c4bf2dcf4406%3AIMG_0277.jpeg?table=block&id=1fc21dda-bbe5-8172-9e57-db1855275ff9&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2: 401 Client Error: Unauthorized for url: https://www.notion.so/image/attachment%3A5ac2cb64-d2e8-445a-9ca2-c4bf2dcf4406%3AIMG_0277.jpeg?table=block&id=1fc21dda-bbe5-8172-9e57-db1855275ff9&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:17:17,324 - ERROR - Failed to process image from https://www.notion.so/image/attachment%3A5ac2cb64-d2e8-445a-9ca2-c4bf2dcf4406%3AIMG_0277.jpeg?table=block&id=1fc21dda-bbe5-8172-9e57-db1855275ff9&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:17:17,325 - ERROR - Row 18: Failed to generate VQA
2025-06-19 16:17:17,326 - INFO - Progress saved: 17 rows completed
2025-06-19 16:17:18,327 - INFO - Row 19: Processing Architecture/찌개집
2025-06-19 16:17:18,327 - INFO - Row 19: Generating VQA without image
2025-06-19 16:17:26,153 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 16:17:26,155 - INFO - Raw API response type (text-only): <class 'str'>
2025-06-19 16:17:26,155 - INFO - Raw API response is None (text-only): False
2025-06-19 16:17:26,155 - INFO - Content length after strip (text-only): 0
2025-06-19 16:17:26,155 - INFO - Raw API response (text-only): ''...
2025-06-19 16:17:26,155 - INFO - FULL API response (text-only): ''
2025-06-19 16:17:26,155 - INFO - Cleaned content for JSON parsing (text-only): ''...
2025-06-19 16:17:26,155 - ERROR - Content is empty after cleaning (text-only)
2025-06-19 16:17:26,155 - ERROR - Row 19: Failed to generate VQA
2025-06-19 16:17:26,156 - INFO - Progress saved: 18 rows completed
2025-06-19 16:17:27,158 - INFO - Row 20: Processing Architecture/국밥집
2025-06-19 16:17:27,158 - INFO - Row 20: Generating VQA without image
2025-06-19 16:17:36,106 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 16:17:36,108 - INFO - Raw API response type (text-only): <class 'str'>
2025-06-19 16:17:36,108 - INFO - Raw API response is None (text-only): False
2025-06-19 16:17:36,109 - INFO - Content length after strip (text-only): 0
2025-06-19 16:17:36,109 - INFO - Raw API response (text-only): ''...
2025-06-19 16:17:36,109 - INFO - FULL API response (text-only): ''
2025-06-19 16:17:36,109 - INFO - Cleaned content for JSON parsing (text-only): ''...
2025-06-19 16:17:36,109 - ERROR - Content is empty after cleaning (text-only)
2025-06-19 16:17:36,109 - ERROR - Row 20: Failed to generate VQA
2025-06-19 16:17:36,110 - INFO - Progress saved: 19 rows completed
2025-06-19 16:17:37,111 - INFO - Row 21: Processing Architecture/해동용궁사
2025-06-19 16:17:37,112 - INFO - Accepting image URL: https://www.notion.so/image/attachment%3Ad145d5f2-4505-4cf0-b02e-cd7b9eb75b18%3AIMG_1855.jpeg?table=...
2025-06-19 16:17:37,112 - INFO - Row 21: Generating VQA with image
2025-06-19 16:17:37,112 - INFO - Downloading image from URL: https://www.notion.so/image/attachment%3Ad145d5f2-4505-4cf0-b02e-cd7b9eb75b18%3AIMG_1855.jpeg?table=block&id=1fc21dda-bbe5-8116-819c-d25bafc7cae6&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:17:37,512 - ERROR - Failed to download/encode image from https://www.notion.so/image/attachment%3Ad145d5f2-4505-4cf0-b02e-cd7b9eb75b18%3AIMG_1855.jpeg?table=block&id=1fc21dda-bbe5-8116-819c-d25bafc7cae6&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2: 401 Client Error: Unauthorized for url: https://www.notion.so/image/attachment%3Ad145d5f2-4505-4cf0-b02e-cd7b9eb75b18%3AIMG_1855.jpeg?table=block&id=1fc21dda-bbe5-8116-819c-d25bafc7cae6&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:17:37,512 - ERROR - Failed to process image from https://www.notion.so/image/attachment%3Ad145d5f2-4505-4cf0-b02e-cd7b9eb75b18%3AIMG_1855.jpeg?table=block&id=1fc21dda-bbe5-8116-819c-d25bafc7cae6&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:17:37,512 - ERROR - Row 21: Failed to generate VQA
2025-06-19 16:17:37,513 - INFO - Progress saved: 20 rows completed
2025-06-19 16:17:38,515 - INFO - Row 22: Processing Architecture/재래시장
2025-06-19 16:17:38,515 - INFO - Row 22: Generating VQA without image
2025-06-19 16:17:43,293 - INFO - Generation interrupted by user
2025-06-19 16:21:47,274 - INFO - Loaded generation rules from /tmp/tmpaq_2p2t7.md
2025-06-19 16:21:47,279 - INFO - Loaded generation rules from /tmp/tmpp0aeppe6.md
2025-06-19 16:21:47,280 - WARNING - URL doesn't look like an image: ftp://example.com...
2025-06-19 16:21:47,284 - INFO - Loaded generation rules from /tmp/tmpaoy_7pa2.md
2025-06-19 16:21:47,285 - INFO - Progress will be saved to: tmpao4nmupt_vqa_progress.csv
2025-06-19 16:21:47,285 - INFO - Row 2: Processing Architecture/한옥
2025-06-19 16:21:47,285 - INFO - Row 2: Attempting VQA with image
2025-06-19 16:21:47,285 - INFO - Row 2: Successfully generated VQA
2025-06-19 16:21:47,285 - INFO - Progress saved: 1 rows completed
2025-06-19 16:21:48,287 - INFO - Row 3: Processing Food/김치
2025-06-19 16:21:48,287 - INFO - Row 3: Attempting VQA without image (fallback)
2025-06-19 16:21:48,287 - INFO - Row 3: Successfully generated VQA
2025-06-19 16:21:48,292 - INFO - Progress saved: 2 rows completed
2025-06-19 16:21:49,293 - INFO - Row 4: Skipping empty row
2025-06-19 16:21:49,299 - INFO - Loaded generation rules from /tmp/tmp5fb5_f10.md
2025-06-19 16:22:05,359 - INFO - Initializing VQA Generator...
2025-06-19 16:22:05,378 - INFO - Loaded generation rules from /mnt/raid6/junkim100/east-asia/VQA_Generation_Rules.md
2025-06-19 16:22:05,378 - INFO - Processing CSV file...
2025-06-19 16:22:05,378 - INFO - Progress will be saved to: VQA _vqa_progress.csv
2025-06-19 16:22:05,378 - INFO - Row 2: Processing Architecture/제주 돌집
2025-06-19 16:22:05,378 - INFO - Accepting image URL: https://www.notion.so/image/attachment%3A619b2f68-f70e-435a-b659-b51be324c20a%3AScreenshot_2025-05-2...
2025-06-19 16:22:05,378 - INFO - Row 2: Attempting VQA with image
2025-06-19 16:22:05,378 - INFO - Downloading image from URL: https://www.notion.so/image/attachment%3A619b2f68-f70e-435a-b659-b51be324c20a%3AScreenshot_2025-05-23_at_2.47.10_PM.png?table=block&id=1fc21dda-bbe5-8146-904f-f3d7e20dda3f&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:22:05,378 - INFO - Trying download strategy 1
2025-06-19 16:22:05,617 - INFO - Trying download strategy 2
2025-06-19 16:22:06,248 - INFO - Trying download strategy 3
2025-06-19 16:22:06,248 - INFO - Trying Notion URL without parameters: https://www.notion.so/image/attachment%3A619b2f68-f70e-435a-b659-b51be324c20a%3AScreenshot_2025-05-23_at_2.47.10_PM.png
2025-06-19 16:22:06,488 - ERROR - All download strategies failed for https://www.notion.so/image/attachment%3A619b2f68-f70e-435a-b659-b51be324c20a%3AScreenshot_2025-05-23_at_2.47.10_PM.png?table=block&id=1fc21dda-bbe5-8146-904f-f3d7e20dda3f&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:22:06,488 - ERROR - Failed to process image from https://www.notion.so/image/attachment%3A619b2f68-f70e-435a-b659-b51be324c20a%3AScreenshot_2025-05-23_at_2.47.10_PM.png?table=block&id=1fc21dda-bbe5-8146-904f-f3d7e20dda3f&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:22:06,488 - INFO - Row 2: Attempting VQA without image (fallback)
2025-06-19 16:22:17,656 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 16:22:17,659 - INFO - Raw API response type (text-only): <class 'str'>
2025-06-19 16:22:17,659 - INFO - Raw API response is None (text-only): False
2025-06-19 16:22:17,660 - INFO - Content length after strip (text-only): 0
2025-06-19 16:22:17,660 - INFO - Raw API response (text-only): ''...
2025-06-19 16:22:17,660 - INFO - FULL API response (text-only): ''
2025-06-19 16:22:17,660 - INFO - Cleaned content for JSON parsing (text-only): ''...
2025-06-19 16:22:17,660 - ERROR - Content is empty after cleaning (text-only)
2025-06-19 16:22:17,660 - WARNING - Row 2: Forcing generic VQA generation
2025-06-19 16:22:17,660 - INFO - Force generating VQA for Architecture/제주 돌집
2025-06-19 16:22:25,496 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 16:22:25,497 - INFO - Force generation response: ...
2025-06-19 16:22:25,498 - WARNING - Force generation JSON parsing failed, creating basic question
2025-06-19 16:22:25,498 - INFO - Row 2: Successfully generated VQA
2025-06-19 16:22:25,498 - INFO - Progress saved: 1 rows completed
2025-06-19 16:22:26,499 - INFO - Row 3: Processing Architecture/월정교
2025-06-19 16:22:26,499 - INFO - Accepting image URL: https://www.notion.so/image/attachment%3A52ff9a7c-cf16-4f4a-baee-a84a4592cfff%3AIMG_0656.jpeg?table=...
2025-06-19 16:22:26,499 - INFO - Row 3: Attempting VQA with image
2025-06-19 16:22:26,500 - INFO - Downloading image from URL: https://www.notion.so/image/attachment%3A52ff9a7c-cf16-4f4a-baee-a84a4592cfff%3AIMG_0656.jpeg?table=block&id=1fc21dda-bbe5-815c-8c4f-fbdd92a718eb&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:22:26,500 - INFO - Trying download strategy 1
2025-06-19 16:22:26,802 - INFO - Trying download strategy 2
2025-06-19 16:22:27,036 - INFO - Trying download strategy 3
2025-06-19 16:22:27,036 - INFO - Trying Notion URL without parameters: https://www.notion.so/image/attachment%3A52ff9a7c-cf16-4f4a-baee-a84a4592cfff%3AIMG_0656.jpeg
2025-06-19 16:22:27,401 - ERROR - All download strategies failed for https://www.notion.so/image/attachment%3A52ff9a7c-cf16-4f4a-baee-a84a4592cfff%3AIMG_0656.jpeg?table=block&id=1fc21dda-bbe5-815c-8c4f-fbdd92a718eb&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:22:27,402 - ERROR - Failed to process image from https://www.notion.so/image/attachment%3A52ff9a7c-cf16-4f4a-baee-a84a4592cfff%3AIMG_0656.jpeg?table=block&id=1fc21dda-bbe5-815c-8c4f-fbdd92a718eb&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:22:27,402 - INFO - Row 3: Attempting VQA without image (fallback)
2025-06-19 16:22:45,219 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 16:22:45,221 - INFO - Raw API response type (text-only): <class 'str'>
2025-06-19 16:22:45,221 - INFO - Raw API response is None (text-only): False
2025-06-19 16:22:45,221 - INFO - Content length after strip (text-only): 542
2025-06-19 16:22:45,222 - INFO - Raw API response (text-only): '{"question":"Which restoration approach was central to the reconstruction of the late Silla-era wooden-span bridge leading to a royal pond in the former capital, as guided by excavated pillar-hole pat'...
2025-06-19 16:22:45,222 - INFO - FULL API response (text-only): '{"question":"Which restoration approach was central to the reconstruction of the late Silla-era wooden-span bridge leading to a royal pond in the former capital, as guided by excavated pillar-hole patterns?","option_1":"Precise replication of mortise-and-tenon joinery based on pillar-hole traces","option_2":"Application of hidden steel reinforcements to uphold span lengths","option_3":"Use of modern glue-laminated timber for ease of installation","option_4":"Concrete footings clad in timber for visual authenticity","correct_option":"A"}'
2025-06-19 16:22:45,222 - INFO - Cleaned content for JSON parsing (text-only): '{"question":"Which restoration approach was central to the reconstruction of the late Silla-era wooden-span bridge leading to a royal pond in the former capital, as guided by excavated pillar-hole pat'...
2025-06-19 16:22:45,222 - INFO - Row 3: Successfully generated VQA
2025-06-19 16:22:45,222 - INFO - Progress saved: 2 rows completed
2025-06-19 16:22:46,223 - INFO - Row 4: Processing Architecture/운현궁
2025-06-19 16:22:46,224 - INFO - Accepting image URL: https://www.notion.so/image/attachment%3Ae997452e-ac86-4583-9980-de73b2c475ca%3AIMG_1269.jpeg?table=...
2025-06-19 16:22:46,224 - INFO - Row 4: Attempting VQA with image
2025-06-19 16:22:46,224 - INFO - Downloading image from URL: https://www.notion.so/image/attachment%3Ae997452e-ac86-4583-9980-de73b2c475ca%3AIMG_1269.jpeg?table=block&id=1fc21dda-bbe5-813b-afc9-fd73a8b1fe3b&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:22:46,224 - INFO - Trying download strategy 1
2025-06-19 16:22:47,080 - INFO - Trying download strategy 2
2025-06-19 16:22:47,544 - INFO - Trying download strategy 3
2025-06-19 16:22:47,544 - INFO - Trying Notion URL without parameters: https://www.notion.so/image/attachment%3Ae997452e-ac86-4583-9980-de73b2c475ca%3AIMG_1269.jpeg
2025-06-19 16:22:47,740 - ERROR - All download strategies failed for https://www.notion.so/image/attachment%3Ae997452e-ac86-4583-9980-de73b2c475ca%3AIMG_1269.jpeg?table=block&id=1fc21dda-bbe5-813b-afc9-fd73a8b1fe3b&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:22:47,741 - ERROR - Failed to process image from https://www.notion.so/image/attachment%3Ae997452e-ac86-4583-9980-de73b2c475ca%3AIMG_1269.jpeg?table=block&id=1fc21dda-bbe5-813b-afc9-fd73a8b1fe3b&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:22:47,741 - INFO - Row 4: Attempting VQA without image (fallback)
2025-06-19 16:23:00,092 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 16:23:00,093 - INFO - Raw API response type (text-only): <class 'str'>
2025-06-19 16:23:00,093 - INFO - Raw API response is None (text-only): False
2025-06-19 16:23:00,093 - INFO - Content length after strip (text-only): 0
2025-06-19 16:23:00,093 - INFO - Raw API response (text-only): ''...
2025-06-19 16:23:00,093 - INFO - FULL API response (text-only): ''
2025-06-19 16:23:00,093 - INFO - Cleaned content for JSON parsing (text-only): ''...
2025-06-19 16:23:00,093 - ERROR - Content is empty after cleaning (text-only)
2025-06-19 16:23:00,093 - WARNING - Row 4: Forcing generic VQA generation
2025-06-19 16:23:00,093 - INFO - Force generating VQA for Architecture/운현궁
2025-06-19 16:23:05,275 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 16:23:05,276 - INFO - Force generation response: ...
2025-06-19 16:23:05,276 - WARNING - Force generation JSON parsing failed, creating basic question
2025-06-19 16:23:05,276 - INFO - Row 4: Successfully generated VQA
2025-06-19 16:23:05,276 - INFO - Progress saved: 3 rows completed
2025-06-19 16:23:06,278 - INFO - Row 5: Processing Architecture/명동
2025-06-19 16:23:06,278 - INFO - Accepting image URL: https://images.unsplash.com/photo-1677097610167-c2d62b06fad3?q=80&w=2670&auto=format&fit=crop&ixlib=...
2025-06-19 16:23:06,278 - INFO - Row 5: Attempting VQA with image
2025-06-19 16:23:06,278 - INFO - Downloading image from URL: https://images.unsplash.com/photo-1677097610167-c2d62b06fad3?q=80&w=2670&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D
2025-06-19 16:23:06,278 - INFO - Trying download strategy 1
2025-06-19 16:23:11,440 - INFO - Resized image to (2048, 1365)
2025-06-19 16:23:11,449 - INFO - Successfully processed image, size: 654973 bytes
2025-06-19 16:23:22,785 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 16:23:22,787 - INFO - Raw API response type: <class 'str'>
2025-06-19 16:23:22,787 - INFO - Raw API response is None: False
2025-06-19 16:23:22,787 - INFO - Content length after strip: 583
2025-06-19 16:23:22,787 - INFO - Raw API response: '{"question":"In this image, a wet pedestrian alley is lined by multi-level commercial structures densely covered with neon and backlit panels for international coffee chains, local skincare boutiques,'...
2025-06-19 16:23:22,787 - INFO - FULL API response: '{"question":"In this image, a wet pedestrian alley is lined by multi-level commercial structures densely covered with neon and backlit panels for international coffee chains, local skincare boutiques, and shoe retailers all labeled in Hangul. Given the unique combination of post-1990s urban redevelopment, the concentration of cosmetic stores targeting overseas tourists, and the narrow retail-lined streets visible here, which famous Seoul shopping district is depicted?","option_1":"Myeongdong","option_2":"Insadong","option_3":"Hongdae","option_4":"Gangnam","correct_option":"A"}'
2025-06-19 16:23:22,787 - INFO - Cleaned content for JSON parsing: '{"question":"In this image, a wet pedestrian alley is lined by multi-level commercial structures densely covered with neon and backlit panels for international coffee chains, local skincare boutiques,'...
2025-06-19 16:23:22,787 - INFO - Row 5: Successfully generated VQA
2025-06-19 16:23:22,788 - INFO - Progress saved: 4 rows completed
2025-06-19 16:23:23,789 - INFO - Row 6: Processing Architecture/남산타워
2025-06-19 16:23:23,789 - INFO - Accepting image URL: https://www.notion.so/image/attachment%3A1047482e-93e8-4f25-9590-52f3dfd1baa8%3AIMG_1727.jpeg?table=...
2025-06-19 16:23:23,790 - INFO - Row 6: Attempting VQA with image
2025-06-19 16:23:23,790 - INFO - Downloading image from URL: https://www.notion.so/image/attachment%3A1047482e-93e8-4f25-9590-52f3dfd1baa8%3AIMG_1727.jpeg?table=block&id=1fc21dda-bbe5-81d2-a305-e446ddc791f2&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:23:23,790 - INFO - Trying download strategy 1
2025-06-19 16:23:24,013 - INFO - Trying download strategy 2
2025-06-19 16:23:24,209 - INFO - Trying download strategy 3
2025-06-19 16:23:24,209 - INFO - Trying Notion URL without parameters: https://www.notion.so/image/attachment%3A1047482e-93e8-4f25-9590-52f3dfd1baa8%3AIMG_1727.jpeg
2025-06-19 16:23:24,441 - ERROR - All download strategies failed for https://www.notion.so/image/attachment%3A1047482e-93e8-4f25-9590-52f3dfd1baa8%3AIMG_1727.jpeg?table=block&id=1fc21dda-bbe5-81d2-a305-e446ddc791f2&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:23:24,441 - ERROR - Failed to process image from https://www.notion.so/image/attachment%3A1047482e-93e8-4f25-9590-52f3dfd1baa8%3AIMG_1727.jpeg?table=block&id=1fc21dda-bbe5-81d2-a305-e446ddc791f2&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:23:24,441 - INFO - Row 6: Attempting VQA without image (fallback)
2025-06-19 16:23:36,952 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 16:23:36,954 - INFO - Raw API response type (text-only): <class 'str'>
2025-06-19 16:23:36,954 - INFO - Raw API response is None (text-only): False
2025-06-19 16:23:36,954 - INFO - Content length after strip (text-only): 0
2025-06-19 16:23:36,954 - INFO - Raw API response (text-only): ''...
2025-06-19 16:23:36,954 - INFO - FULL API response (text-only): ''
2025-06-19 16:23:36,954 - INFO - Cleaned content for JSON parsing (text-only): ''...
2025-06-19 16:23:36,954 - ERROR - Content is empty after cleaning (text-only)
2025-06-19 16:23:36,955 - WARNING - Row 6: Forcing generic VQA generation
2025-06-19 16:23:36,955 - INFO - Force generating VQA for Architecture/남산타워
2025-06-19 16:23:41,430 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 16:23:41,432 - INFO - Force generation response: ...
2025-06-19 16:23:41,432 - WARNING - Force generation JSON parsing failed, creating basic question
2025-06-19 16:23:41,432 - INFO - Row 6: Successfully generated VQA
2025-06-19 16:23:41,433 - INFO - Progress saved: 5 rows completed
2025-06-19 16:23:42,434 - INFO - Row 7: Processing Architecture/신라대종
2025-06-19 16:23:42,434 - INFO - Accepting image URL: https://www.notion.so/image/attachment%3A18f7bd18-ce33-488c-9e30-e7f153449bb9%3AIMG_0587.jpeg?table=...
2025-06-19 16:23:42,434 - INFO - Row 7: Attempting VQA with image
2025-06-19 16:23:42,434 - INFO - Downloading image from URL: https://www.notion.so/image/attachment%3A18f7bd18-ce33-488c-9e30-e7f153449bb9%3AIMG_0587.jpeg?table=block&id=1fc21dda-bbe5-81ee-b974-f4ef3de73ceb&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:23:42,434 - INFO - Trying download strategy 1
2025-06-19 16:23:42,640 - INFO - Trying download strategy 2
2025-06-19 16:23:42,865 - INFO - Trying download strategy 3
2025-06-19 16:23:42,865 - INFO - Trying Notion URL without parameters: https://www.notion.so/image/attachment%3A18f7bd18-ce33-488c-9e30-e7f153449bb9%3AIMG_0587.jpeg
2025-06-19 16:23:43,402 - ERROR - All download strategies failed for https://www.notion.so/image/attachment%3A18f7bd18-ce33-488c-9e30-e7f153449bb9%3AIMG_0587.jpeg?table=block&id=1fc21dda-bbe5-81ee-b974-f4ef3de73ceb&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:23:43,402 - ERROR - Failed to process image from https://www.notion.so/image/attachment%3A18f7bd18-ce33-488c-9e30-e7f153449bb9%3AIMG_0587.jpeg?table=block&id=1fc21dda-bbe5-81ee-b974-f4ef3de73ceb&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:23:43,402 - INFO - Row 7: Attempting VQA without image (fallback)
2025-06-19 16:23:52,973 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 16:23:52,974 - INFO - Raw API response type (text-only): <class 'str'>
2025-06-19 16:23:52,975 - INFO - Raw API response is None (text-only): False
2025-06-19 16:23:52,975 - INFO - Content length after strip (text-only): 638
2025-06-19 16:23:52,975 - INFO - Raw API response (text-only): '{\n    "question": "This monumental bronze bell, erected in the late 20th century to commemorate the 1,350th anniversary of a historic kingdom’s unification, is housed in a wooden pavilion whose roof e'...
2025-06-19 16:23:52,975 - INFO - FULL API response (text-only): '{\n    "question": "This monumental bronze bell, erected in the late 20th century to commemorate the 1,350th anniversary of a historic kingdom’s unification, is housed in a wooden pavilion whose roof eaves are supported by elaborate multi-tiered bracket clusters between the columns. Which traditional Korean timber construction principle is exemplified by this pavilion’s use of ornate, interlocking bracket sets?",\n    "option_1": "Dapo style bracket system",\n    "option_2": "Ga-rip style bracket system",\n    "option_3": "Yeol-chu column-spacing method",\n    "option_4": "Hanok wide-beam structural layout",\n    "correct_option": "A"\n}'
2025-06-19 16:23:52,975 - INFO - Cleaned content for JSON parsing (text-only): '{\n    "question": "This monumental bronze bell, erected in the late 20th century to commemorate the 1,350th anniversary of a historic kingdom’s unification, is housed in a wooden pavilion whose roof e'...
2025-06-19 16:23:52,975 - INFO - Row 7: Successfully generated VQA
2025-06-19 16:23:52,976 - INFO - Progress saved: 6 rows completed
2025-06-19 16:23:53,977 - INFO - Row 8: Processing Architecture/고려대학교
2025-06-19 16:23:53,977 - INFO - Accepting image URL: https://www.notion.so/image/attachment%3Aadaf8ea1-e599-40a0-89d5-c292ee2780fd%3AIMG_3619.jpeg?table=...
2025-06-19 16:23:53,977 - INFO - Row 8: Attempting VQA with image
2025-06-19 16:23:53,977 - INFO - Downloading image from URL: https://www.notion.so/image/attachment%3Aadaf8ea1-e599-40a0-89d5-c292ee2780fd%3AIMG_3619.jpeg?table=block&id=1fc21dda-bbe5-81fe-98d5-e8850a9f2ba7&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:23:53,977 - INFO - Trying download strategy 1
2025-06-19 16:23:54,207 - INFO - Trying download strategy 2
2025-06-19 16:23:54,735 - INFO - Trying download strategy 3
2025-06-19 16:23:54,735 - INFO - Trying Notion URL without parameters: https://www.notion.so/image/attachment%3Aadaf8ea1-e599-40a0-89d5-c292ee2780fd%3AIMG_3619.jpeg
2025-06-19 16:23:55,245 - ERROR - All download strategies failed for https://www.notion.so/image/attachment%3Aadaf8ea1-e599-40a0-89d5-c292ee2780fd%3AIMG_3619.jpeg?table=block&id=1fc21dda-bbe5-81fe-98d5-e8850a9f2ba7&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:23:55,245 - ERROR - Failed to process image from https://www.notion.so/image/attachment%3Aadaf8ea1-e599-40a0-89d5-c292ee2780fd%3AIMG_3619.jpeg?table=block&id=1fc21dda-bbe5-81fe-98d5-e8850a9f2ba7&spaceId=6a058a94-86d5-4821-a130-3365a868dbb0&width=2000&userId=d215f426-cc86-4d12-8799-c03a7464fdd2&cache=v2
2025-06-19 16:23:55,245 - INFO - Row 8: Attempting VQA without image (fallback)
2025-06-19 16:24:03,866 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 16:24:03,868 - INFO - Raw API response type (text-only): <class 'str'>
2025-06-19 16:24:03,868 - INFO - Raw API response is None (text-only): False
2025-06-19 16:24:03,868 - INFO - Content length after strip (text-only): 0
2025-06-19 16:24:03,868 - INFO - Raw API response (text-only): ''...
2025-06-19 16:24:03,868 - INFO - FULL API response (text-only): ''
2025-06-19 16:24:03,868 - INFO - Cleaned content for JSON parsing (text-only): ''...
2025-06-19 16:24:03,868 - ERROR - Content is empty after cleaning (text-only)
2025-06-19 16:24:03,868 - WARNING - Row 8: Forcing generic VQA generation
2025-06-19 16:24:03,868 - INFO - Force generating VQA for Architecture/고려대학교
2025-06-19 16:24:09,603 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 16:24:09,605 - INFO - Force generation response: ...
2025-06-19 16:24:09,606 - WARNING - Force generation JSON parsing failed, creating basic question
2025-06-19 16:24:09,606 - INFO - Row 8: Successfully generated VQA
2025-06-19 16:24:09,606 - INFO - Progress saved: 7 rows completed
2025-06-19 16:24:10,608 - INFO - Row 9: Processing Architecture/한강다리
2025-06-19 16:24:10,608 - INFO - Accepting image URL: https://plus.unsplash.com/premium_photo-1716968594404-ac5ae8cdcdc4?q=80&w=2667&auto=format&fit=crop&...
2025-06-19 16:24:10,608 - INFO - Row 9: Attempting VQA with image
2025-06-19 16:24:10,608 - INFO - Downloading image from URL: https://plus.unsplash.com/premium_photo-1716968594404-ac5ae8cdcdc4?q=80&w=2667&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D
2025-06-19 16:24:10,608 - INFO - Trying download strategy 1
2025-06-19 16:24:13,341 - INFO - Resized image to (2048, 1370)
2025-06-19 16:24:13,349 - INFO - Successfully processed image, size: 319732 bytes
2025-06-19 16:24:24,749 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-19 16:24:24,749 - INFO - Raw API response type: <class 'str'>
2025-06-19 16:24:24,750 - INFO - Raw API response is None: False
2025-06-19 16:24:24,750 - INFO - Content length after strip: 0
2025-06-19 16:24:24,750 - INFO - Raw API response: ''...
2025-06-19 16:24:24,750 - INFO - FULL API response: ''
2025-06-19 16:24:24,750 - INFO - Cleaned content for JSON parsing: ''...
2025-06-19 16:24:24,750 - ERROR - Content is empty after cleaning - possibly content filtered
2025-06-19 16:24:24,750 - INFO - Retrying image generation (attempt 1)
2025-06-19 16:24:26,752 - INFO - Downloading image from URL: https://plus.unsplash.com/premium_photo-1716968594404-ac5ae8cdcdc4?q=80&w=2667&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D
2025-06-19 16:24:26,752 - INFO - Trying download strategy 1
2025-06-19 16:24:27,033 - INFO - Resized image to (2048, 1370)
2025-06-19 16:24:27,041 - INFO - Successfully processed image, size: 319732 bytes
2025-06-19 16:24:28,459 - INFO - Generation interrupted by user
2025-06-19 16:27:51,960 - INFO - Loaded generation rules from /tmp/tmpvinz3xwj.md
2025-06-19 16:27:51,965 - INFO - Loaded generation rules from /tmp/tmpb3vnwfyr.md
2025-06-19 16:27:51,965 - WARNING - URL doesn't look like an image: ftp://example.com...
2025-06-19 16:27:51,970 - INFO - Loaded generation rules from /tmp/tmpjbsf__nh.md
2025-06-19 16:27:51,971 - INFO - Progress will be saved to: tmpng9qmh80_vqa_progress.csv
2025-06-19 16:27:51,971 - INFO - Row 2: Processing Architecture/한옥
2025-06-19 16:27:51,971 - INFO - Row 2: Attempting VQA with image
2025-06-19 16:27:51,971 - INFO - Row 2: Successfully generated VQA
2025-06-19 16:27:51,971 - INFO - Progress saved: 1 rows completed
2025-06-19 16:27:52,972 - INFO - Row 3: Processing Food/김치
2025-06-19 16:27:52,972 - INFO - Row 3: Attempting VQA without image (fallback)
2025-06-19 16:27:52,973 - INFO - Row 3: Successfully generated VQA
2025-06-19 16:27:52,977 - INFO - Progress saved: 2 rows completed
2025-06-19 16:27:53,979 - INFO - Row 4: Skipping empty row
2025-06-19 16:27:53,985 - INFO - Loaded generation rules from /tmp/tmpuahemuc0.md
