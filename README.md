# VQA Content Generator

A Python program that uses OpenAI's o4-mini-2025-04-16 model (with enhanced vision capabilities) to generate Visual Question Answering (VQA) content based on Korean cultural images and keywords.

## Features

- **Vision-based VQA Generation**: Analyzes images using o4-mini-2025-04-16's enhanced vision capabilities to generate culturally-aware questions
- **Text-only VQA Generation**: Creates questions based on keywords/categories without requiring images
- **Expert-level Difficulty**: Follows comprehensive generation rules to create challenging, culturally-specific questions
- **Multi-format Output**: Supports both CSV and JSON output formats
- **Error Handling**: Robust handling of invalid URLs, API failures, and malformed data
- **Rate Limiting**: Built-in delays to respect OpenAI API rate limits
- **Comprehensive Logging**: Detailed logging for monitoring and debugging

## Requirements

- Python 3.7+
- OpenAI API key
- Internet connection for image URL validation and API calls

## Installation

1. Clone or download the files:
   ```bash
   # Download the main files
   wget vqa_generator.py
   wget requirements.txt
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Set up your OpenAI API key:
   ```bash
   export OPENAI_API_KEY="your-api-key-here"
   ```

## Usage

### Basic Usage

```bash
python vqa_generator.py --api-key YOUR_API_KEY
```

### Advanced Usage

```bash
# Generate JSON output
python vqa_generator.py --api-key YOUR_API_KEY --output-format json

# Specify custom output file
python vqa_generator.py --api-key YOUR_API_KEY --output-file my_vqa_results.csv

# Use custom rules and CSV files
python vqa_generator.py --api-key YOUR_API_KEY --rules custom_rules.md --csv custom_data.csv

# Dry run (process only first 5 rows for testing)
python vqa_generator.py --api-key YOUR_API_KEY --dry-run
```

### Command Line Options

- `--api-key`: OpenAI API key (required, or set OPENAI_API_KEY environment variable)
- `--rules`: Path to VQA generation rules markdown file (default: VQA_Generation_Rules.md)
- `--csv`: Path to input CSV file (default: VQA .csv)
- `--output-format`: Output format - 'csv' or 'json' (default: csv)
- `--output-file`: Custom output file path (auto-generated if not specified)
- `--dry-run`: Process only first 5 rows for testing

## Input File Format

The program expects a CSV file with the following columns:

- `Main Category`: The main category (e.g., "Architecture", "Food", "Culture Attitude")
- `Question`: (Initially empty - will be filled by the generator)
- `Option 1`: (Initially empty - will be filled by the generator)
- `Option 2`: (Initially empty - will be filled by the generator)
- `Option 3`: (Initially empty - will be filled by the generator)
- `Option 4`: (Initially empty - will be filled by the generator)
- `Correct Option`: (Initially empty - will be filled by the generator)
- `Image Source`: URL to the image (optional)
- `Keyword/Concept`: The keyword or concept to base the question on

## Processing Logic

1. **With Image + Keyword**: Uses vision model to analyze image and generate questions based on both visual content and cultural context
2. **Keyword Only**: Generates questions based purely on cultural knowledge without visual elements
3. **Empty Rows**: Preserves empty rows in the output to maintain CSV structure
4. **Invalid Data**: Logs errors and preserves row structure with empty question fields

## Output Formats

### CSV Output
Maintains the same structure as input CSV but with generated questions and options filled in.

### JSON Output
```json
[
  {
    "main_category": "Architecture",
    "question": "Generated question text...",
    "options": {
      "A": "Option A text",
      "B": "Option B text", 
      "C": "Option C text",
      "D": "Option D text"
    },
    "correct_option": "A",
    "image_source": "https://example.com/image.jpg",
    "keyword_concept": "한옥"
  }
]
```

## Generation Rules

The program follows comprehensive generation rules that ensure:

- **Expert-level difficulty**: Questions require deep cultural knowledge
- **Multi-hop reasoning**: Questions require 2-3 reasoning steps
- **Cultural authenticity**: All content is culturally accurate and specific to Korean culture
- **Plausible distractors**: Incorrect options are culturally plausible but subtly wrong

## Error Handling

- **Invalid URLs**: Validates image URLs before processing
- **API Failures**: Logs errors and continues processing other rows
- **Malformed Data**: Handles missing or invalid CSV data gracefully
- **Rate Limiting**: Includes delays between API calls to prevent rate limit errors

## Logging

The program creates detailed logs in `vqa_generator.log` including:
- Processing status for each row
- API call results
- Error messages and warnings
- Generation statistics

## Example Output

For a row with category "Architecture" and keyword "한옥", the program might generate:

**Question**: "Based on the architectural elements visible in this traditional Korean structure, which cultural protocol would be most essential for visitors to observe when entering the main living quarters during a formal family gathering?"

**Options**:
- A) Remove shoes at the entrance and bow to the eldest family member present
- B) Announce arrival loudly and wait for formal invitation to enter
- C) Enter directly through the main gate and greet everyone simultaneously  
- D) Wait outside until specifically summoned by the household head

**Correct Answer**: A

## Troubleshooting

1. **API Key Issues**: Ensure your OpenAI API key is valid and has sufficient credits
2. **Image URL Problems**: Check that image URLs are accessible and not behind authentication
3. **CSV Format Issues**: Ensure CSV file has proper headers and encoding (UTF-8)
4. **Rate Limiting**: If you encounter rate limits, the program includes built-in delays, but you may need to reduce concurrent usage

## License

This project is provided as-is for educational and research purposes.
